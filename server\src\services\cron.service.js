const cron = require('node-cron');
const { Provider } = require('../models');
const providerFactory = require('./provider.factory');
const { EsimPlan } = require('../models');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');

class CronService {
    constructor() {
        // Schedule plan sync to run every 4 hours
        // Cron format: Minute Hour Day Month Day_of_week
        // '0 */4 * * *' means: At minute 0 past every 4th hour
        this.planSyncJob = cron.schedule('0 */4 * * *', async () => {
            console.log('Starting automatic plan sync...');
            await this.syncExternalPlans();
        }, {
            scheduled: true,
            timezone: "UTC"
        });
    }
    
    async syncExternalPlans() {
        try {
            // Get all active external providers
            const providers = await Provider.findAll({
                where: { 
                    type: 'API',
                    status: 'active'
                }
            });

            const results = {
                total: 0,
                updated: 0,
                created: 0,
                failed: 0,
                errors: []
            };

            // Categories to sync
            const categories = ['esim_realtime', 'esim_addon', 'esim_replacement'];

            for (const provider of providers) {
                try {
                    const providerService = providerFactory.getProvider(provider.name);
                    
                    // Fetch plans for each category
                    for (const category of categories) {
                        try {
                            console.log(`[Cron] Fetching ${category} plans from ${provider.name}...`);
                            
                            // Use getProductsWithPrices for BillionConnect to ensure prices are fetched
                            const externalPlans = provider.name.toLowerCase() === 'billionconnect' 
                                ? await providerService.getProductsWithPrices()
                                : await providerService.getProducts(category);
                            
                            if (!Array.isArray(externalPlans)) {
                                throw new Error(`Invalid response from provider ${provider.name} for category ${category}`);
                            }

                            console.log(`[Cron] Found ${externalPlans.length} ${category} plans from ${provider.name}`);
                            
                            for (const externalPlan of externalPlans) {
                                try {
                                    results.total++;
                                    const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);
                                    
                                    // Check if plan already exists
                                    let plan = await EsimPlan.findOne({
                                        where: {
                                            [Op.or]: [
                                                { externalProductId: standardizedPlan.externalProductId },
                                                { externalSkuId: standardizedPlan.externalSkuId }
                                            ]
                                        }
                                    });

                                    if (plan) {
                                        // Update existing plan while preserving manually set fields
                                        const fieldsToPreserve = {
                                            sellingPrice: plan.sellingPrice,
                                            status: plan.status,
                                            stockThreshold: plan.stockThreshold,
                                            startDateEnabled: plan.startDateEnabled,
                                            features: plan.features,
                                            instructions: plan.instructions,
                                            description: plan.description,
                                        };

                                        // Update plan with new data while preserving manual fields
                                        await plan.update({
                                            ...standardizedPlan,
                                            ...fieldsToPreserve,
                                            providerId: provider.id,
                                            updatedAt: new Date()
                                        });
                                        results.updated++;
                                    } else {
                                        // Create new plan
                                        await EsimPlan.create({
                                            ...standardizedPlan,
                                            id: uuidv4(),
                                            providerId: provider.id,
                                            status: 'visible'
                                        });
                                        results.created++;
                                    }
                                } catch (error) {
                                    console.error(`[Cron] Error processing plan from ${provider.name}:`, error);
                                    results.failed++;
                                    results.errors.push({
                                        provider: provider.name,
                                        planId: externalPlan.id,
                                        category: category,
                                        error: error.message
                                    });
                                }
                            }
                        } catch (error) {
                            console.error(`[Cron] Error fetching ${category} plans from ${provider.name}:`, error);
                            results.errors.push({
                                provider: provider.name,
                                category,
                                error: error.message
                            });
                        }
                    }
                } catch (error) {
                    console.error(`[Cron] Error processing provider ${provider.name}:`, error);
                    results.errors.push({
                        provider: provider.name,
                        error: error.message
                    });
                }
            }

            console.log('[Cron] Plan sync completed:', results);
            return results;
        } catch (error) {
            console.error('[Cron] Error in automatic plan sync:', error);
            throw error;
        }
    }

    startJobs() {
        this.planSyncJob.start();
        console.log('Cron jobs started');
    }

    stopJobs() {
        this.planSyncJob.stop();
        console.log('Cron jobs stopped');
    }
}

module.exports = new CronService(); 