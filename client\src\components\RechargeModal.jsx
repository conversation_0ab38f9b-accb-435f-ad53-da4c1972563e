import React, { useState, useEffect } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2, Zap, Globe, Calendar, Database, CreditCard, AlertCircle } from "lucide-react";
import { useToast } from '@/components/ui/use-toast';
import api from '@/lib/axios';

const RechargeModal = ({ isOpen, onClose, orderId, orderInfo }) => {
    const [loading, setLoading] = useState(false);
    const [rechargeData, setRechargeData] = useState(null);
    const [selectedPlan, setSelectedPlan] = useState(null);
    const [creating, setCreating] = useState(false);
    const { toast } = useToast();

    useEffect(() => {
        if (isOpen && orderId) {
            fetchRechargeablePlans();
        }
    }, [isOpen, orderId]);

    const fetchRechargeablePlans = async () => {
        try {
            setLoading(true);
            const response = await api.get(`/api/esim-plans/recharge-plans/${orderId}`);
            setRechargeData(response.data);
        } catch (error) {
            console.error('Error fetching rechargeable plans:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch rechargeable plans"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleRecharge = async () => {
        if (!selectedPlan || !rechargeData?.orderInfo) return;

        try {
            setCreating(true);
            
            const rechargeOrderData = {
                channelOrderId: `RECHARGE_${Date.now()}_${orderId}`,
                totalAmount: selectedPlan.price.toString(),
                orderCreateTime: new Date().toISOString().replace('T', ' ').split('.')[0],
                comment: `Recharge order for existing eSIM - Order #${orderId}`,
                subOrderList: [
                    {
                        channelSubOrderId: `SUB_RECHARGE_${Date.now()}`,
                        iccid: [rechargeData.orderInfo.iccid],
                        skuId: selectedPlan.externalSkuId,
                        copies: "1",
                        price: selectedPlan.price.toString()
                    }
                ]
            };

            const response = await api.post('/api/esim-plans/recharge-order', rechargeOrderData);

            toast({
                title: "Recharge Order Created!",
                description: `Successfully created recharge order for ${selectedPlan.name}`,
            });

            onClose();
            // Optionally refresh the parent component
            if (window.location) {
                window.location.reload();
            }

        } catch (error) {
            console.error('Error creating recharge order:', error);
            toast({
                variant: "destructive",
                title: "Recharge Failed",
                description: error.response?.data?.details || "Failed to create recharge order"
            });
        } finally {
            setCreating(false);
        }
    };

    const formatData = (data) => {
        if (!data) return 'N/A';
        if (data.toLowerCase().includes('unlimited')) return 'Unlimited';
        return data;
    };

    const formatValidity = (validity) => {
        if (!validity) return 'N/A';
        return validity;
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-orange-600" />
                        Recharge eSIM
                    </DialogTitle>
                    <DialogDescription>
                        Add more data to your existing eSIM card
                    </DialogDescription>
                </DialogHeader>

                {loading ? (
                    <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                        <span className="ml-2">Loading rechargeable plans...</span>
                    </div>
                ) : !rechargeData?.available ? (
                    <div className="text-center py-8">
                        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Recharge Not Available</h3>
                        <p className="text-gray-600 mb-4">
                            {rechargeData?.reason || 'Recharge is not available for this order'}
                        </p>
                        <Button onClick={onClose} variant="outline">
                            Close
                        </Button>
                    </div>
                ) : (
                    <div className="space-y-6">
                        {/* Current Order Info */}
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 className="font-medium text-blue-900 mb-2">Current eSIM Details</h4>
                            <div className="text-sm text-blue-800">
                                <p><strong>Order:</strong> #{rechargeData.orderInfo.id}</p>
                                <p><strong>Plan:</strong> {rechargeData.orderInfo.planName}</p>
                                <p><strong>ICCID:</strong> {rechargeData.orderInfo.iccid}</p>
                            </div>
                        </div>

                        {/* Available Plans */}
                        <div>
                            <h4 className="font-medium text-gray-900 mb-4">Select a Recharge Plan</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {rechargeData.rechargeablePlans.map((plan) => (
                                    <Card 
                                        key={plan.id}
                                        className={`cursor-pointer transition-all duration-200 ${
                                            selectedPlan?.id === plan.id 
                                                ? 'ring-2 ring-blue-500 bg-blue-50' 
                                                : 'hover:shadow-md'
                                        }`}
                                        onClick={() => setSelectedPlan(plan)}
                                    >
                                        <CardContent className="p-4">
                                            <div className="flex justify-between items-start mb-3">
                                                <h5 className="font-medium text-gray-900 text-sm">{plan.name}</h5>
                                                <Badge variant="secondary" className="text-xs">
                                                    {plan.currency} {plan.price}
                                                </Badge>
                                            </div>
                                            
                                            <div className="space-y-2 text-xs text-gray-600">
                                                <div className="flex items-center gap-2">
                                                    <Database className="h-3 w-3" />
                                                    <span>Data: {formatData(plan.data)}</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="h-3 w-3" />
                                                    <span>Validity: {formatValidity(plan.validity)}</span>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Globe className="h-3 w-3" />
                                                    <span>Countries: {plan.countries?.length || 0}</span>
                                                </div>
                                            </div>

                                            {plan.description && (
                                                <p className="text-xs text-gray-500 mt-2 line-clamp-2">
                                                    {plan.description}
                                                </p>
                                            )}
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 pt-4 border-t">
                            <Button onClick={onClose} variant="outline">
                                Cancel
                            </Button>
                            <Button 
                                onClick={handleRecharge}
                                disabled={!selectedPlan || creating}
                                className="bg-orange-600 hover:bg-orange-700"
                            >
                                {creating ? (
                                    <>
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                        Creating Recharge...
                                    </>
                                ) : (
                                    <>
                                        <CreditCard className="h-4 w-4 mr-2" />
                                        Recharge for {selectedPlan ? `${selectedPlan.currency} ${selectedPlan.price}` : '...'}
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                )}
            </DialogContent>
        </Dialog>
    );
};

export default RechargeModal;
