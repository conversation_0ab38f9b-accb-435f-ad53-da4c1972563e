import React, { useState, useEffect } from 'react';
import { useNavigate, useOutletContext } from 'react-router-dom';
import { format } from 'date-fns';
import api from '../../lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
    Loader2, 
    ShoppingCart, 
    CalendarIcon, 
    Trash2,
    Package,
    Wifi,
    Globe,
    Phone,
    PlusCircle,
    ListChecks
} from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const Cart = () => {
    const navigate = useNavigate();
    const { toast } = useToast();
    const { fetchCartCount } = useOutletContext();
    const [cartItems, setCartItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [startDates, setStartDates] = useState({});
    const [placingOrder, setPlacingOrder] = useState(false);

    useEffect(() => {
        fetchCart();
    }, []);

    const fetchCart = async () => {
        try {
            setLoading(true);
            const response = await api.get('/api/cart');
            setCartItems(response.data.cartItems);
            
            // Initialize start dates for items that require it
            const dates = {};
            response.data.cartItems.forEach(item => {
                if (item.EsimPlan.startDateEnabled) {
                    dates[item.id] = null;
                }
            });
            setStartDates(dates);
        } catch (error) {
            console.error('Error fetching cart:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to fetch cart items"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleStartDateChange = async (itemId, date) => {
        try {
            setStartDates(prev => ({
                ...prev,
                [itemId]: date
            }));

            // When start date is set, update activation policy
            if (date) {
                await api.post(`/api/cart/${itemId}/activation-policy`, {
                    activationPolicy: 'Activation upon travel date'
                });
            }
        } catch (error) {
            console.error('Error updating activation policy:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to update activation policy"
            });
        }
    };

    const handleRemoveItem = async (itemId) => {
        try {
            await api.delete(`/api/cart`);
            await fetchCart();
            await fetchCartCount();
            toast({
                title: "Success",
                description: "Item removed from cart"
            });
        } catch (error) {
            console.error('Error removing item:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: "Failed to remove item from cart"
            });
        }
    };

    const handlePlaceOrder = async () => {
        // Validate start dates for plans that require them
        const missingStartDates = cartItems.some(item => 
            item.EsimPlan.startDateEnabled && !startDates[item.id]
        );

        if (missingStartDates) {
            toast({
                variant: "destructive",
                title: "Error",
                description: "Please select start dates for all required plans"
            });
            return;
        }

        try {
            setPlacingOrder(true);
            const checkoutData = {
                items: cartItems.map(item => ({
                    id: item.id,
                    startDate: startDates[item.id] ? format(startDates[item.id], 'yyyy-MM-dd') : null
                }))
            };
            const response = await api.post('/api/orders', checkoutData);
            await fetchCartCount();
            toast({
                title: "Success",
                description: "Order placed successfully"
            });
            // Navigate to the first order's details page
            // The API now returns an array of orders, so we take the first one
            navigate(`/dashboard/orders/${response.data.orders[0]}`);
        } catch (error) {
            console.error('Error placing order:', error);
            toast({
                variant: "destructive",
                title: "Error",
                description: error.response?.data?.message || "Failed to place order"
            });
        } finally {
            setPlacingOrder(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-[calc(100vh-4rem)]">
                <div className="text-center space-y-4">
                    <Loader2 className="w-12 h-12 animate-spin mx-auto text-primary" />
                    <p className="text-gray-600 text-lg font-medium">Loading your cart...</p>
                </div>
            </div>
        );
    }

    if (!cartItems.length) {
        return (
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <Card className="w-full max-w-3xl mx-auto shadow-md border-0 overflow-hidden">
                    <CardHeader className="border-b bg-gradient-to-r from-blue-800 to-blue-600 py-8">
                        <CardTitle className="text-3xl font-bold flex items-center justify-center space-x-3 text-white">
                            <ShoppingCart className="w-8 h-8" />
                            <span>Your Cart is Empty</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-10">
                        <div className="flex flex-col items-center justify-center space-y-8">
                            <div className="rounded-full bg-gray-100 p-8">
                                <ShoppingCart className="w-20 h-20 text-gray-400" />
                            </div>
                            <p className="text-xl text-gray-700 font-medium text-center max-w-md">
                                Looks like you haven't added any eSIM plans to your cart yet. Browse our collection to find the perfect plan for your needs.
                            </p>
                            <Button
                                className="px-10 py-6 text-lg font-semibold rounded-full bg-blue-600 text-white"
                                size="lg"
                                onClick={() => navigate('/dashboard/plans')}
                            >
                                Browse Plans
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        );
    }

    const total = cartItems.reduce((sum, item) => {
        const price = parseFloat(item.EsimPlan.sellingPrice) || 0;
        return sum + price;
    }, 0);

    return (
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <Card className="w-full max-w-5xl mx-auto shadow-md border-0 overflow-hidden">
                <CardHeader className="border-b bg-gradient-to-r from-blue-800 to-blue-600 py-6">
                    <div className="flex items-center justify-between">
                        <CardTitle className="text-3xl font-bold flex items-center space-x-3 text-white">
                            <ShoppingCart className="w-8 h-8" />
                            <span>Shopping Cart</span>
                        </CardTitle>
                        <Badge className="py-2 px-4 text-sm bg-white text-primary hover:bg-gray-100">
                            {cartItems.length} item{cartItems.length > 1 ? 's' : ''}
                        </Badge>
                    </div>
                </CardHeader>

                <CardContent className="p-0">
                    <div className="divide-y divide-gray-200">
                        {cartItems.map((item) => (
                            <div
                                key={item.id}
                                className="p-6 hover:bg-gray-50 transition-colors duration-200"
                            >
                                <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-6">
                                    <div className="flex-1">
                                        <div className="flex flex-wrap items-center gap-3 mb-4">
                                            <h3 className="text-2xl font-semibold text-gray-800">
                                                {item.EsimPlan.name}
                                            </h3>
                                            <Badge className="bg-primary/10 text-primary hover:bg-primary/20 border-0" variant="outline">
                                                {item.EsimPlan.planType}
                                            </Badge>
                                        </div>

                                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-6 mt-4">
                                            <div className="flex items-center space-x-3">
                                                <CalendarIcon className="w-5 h-5 text-primary flex-shrink-0" />
                                                <div>
                                                    <span className="text-xs text-gray-500 block">Validity</span>
                                                    <span className="font-medium">{item.EsimPlan.validityDays} days</span>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-3">
                                                <ListChecks className="w-5 h-5 text-primary flex-shrink-0" />
                                                <div>
                                                    <span className="text-xs text-gray-500 block">Plan Category</span>
                                                    <span className="font-medium text-center">{item.EsimPlan.planCategory}</span>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-3">
                                                <Globe className="w-5 h-5 text-primary flex-shrink-0" />
                                                <div>
                                                    <span className="text-xs text-gray-500 block">Region</span>
                                                    <span className="font-medium">
                                                        {Array.isArray(item.EsimPlan.region)
                                                            ? item.EsimPlan.region.slice(0, 2).join(', ')
                                                            : item.EsimPlan.region}
                                                    </span>
                                                    {item.EsimPlan.region.length > 2 && (
                                                        <span className="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-50">
                                                            +{item.EsimPlan.region.length - 2} more
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-3">   
                                                <Package className="w-5 h-5 text-primary flex-shrink-0" />
                                                <div>
                                                    <span className="text-xs text-gray-500 block">Data</span>
                                                    <span className="font-medium">
                                                        {item.EsimPlan.planType === 'Unlimited'
                                                            ? 'Unlimited'
                                                            : item.EsimPlan.planType === 'Custom'
                                                                ? item.EsimPlan.customPlanData
                                                                : `${item.EsimPlan.planData} ${item.EsimPlan.planDataUnit}`}
                                                    </span>
                                                </div>
                                            </div>
                                            {item.EsimPlan.planCategory === 'Voice and Data' && (
                                                <div className="flex items-center space-x-3">
                                                    <Phone className="w-5 h-5 text-primary flex-shrink-0" />
                                                    <div>
                                                        <span className="text-xs text-gray-500 block">Voice</span>
                                                        <span className="font-medium">
                                                            {item.EsimPlan.voiceMin} {item.EsimPlan.voiceMinUnit}
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                            <div className="flex items-center space-x-3">
                                                <PlusCircle className="w-5 h-5 text-primary flex-shrink-0" />
                                                <div>
                                                    <span className="text-xs text-gray-500 block">Quantity</span>
                                                    <span className="font-medium text-center">{item.quantity}</span>
                                                </div>
                                            </div>
                                            {item.EsimPlan.startDateEnabled && (
                                                <div className="flex items-center space-x-3 mt-2">
                                                    <CalendarIcon className="w-5 h-5 text-primary flex-shrink-0" />
                                                    <div className="flex-1">
                                                        <Label className="text-xs text-gray-500 mb-1 block">Start Date *</Label>
                                                        <DatePicker
                                                            selected={startDates[item.id]}
                                                            onChange={(date) => handleStartDateChange(item.id, date)}
                                                            minDate={new Date()}
                                                            dateFormat="MMMM dd, yyyy"
                                                            className="w-full px-3 py-2 rounded-md border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                                                        />
                                                        <div className="text-red-500 text-sm mt-2 ml-2 mb-2">
                                                            Start Date is the date when the eSIM will be activated
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <div className="flex flex-row md:flex-col justify-between items-center md:items-end gap-4 pt-4 md:pt-0">
                                        <p className="text-2xl font-bold text-primary order-1 md:order-2">
                                            ${parseFloat(item.EsimPlan.sellingPrice).toFixed(2)}
                                        </p>
                                        <Button
                                            variant="ghost"
                                            className="bg-blue-600 hover:bg-red-50 hover:text-red-600 rounded-full h-10 w-10 p-0 order-2 md:order-1"
                                            onClick={() => handleRemoveItem(item.id)}
                                            aria-label="Remove item"
                                        >
                                            <Trash2 className="w-5 h-5" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="mx-6 my-8">
                        <Separator className="my-4" />
                        
                        <div className="rounded-lg bg-gray-50 border border-gray-200 p-6">
                            <div className="flex justify-between items-center">
                                <span className="text-gray-600">Subtotal</span>
                                <span className="font-medium">${total.toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between items-center mt-2">
                                <span className="text-gray-600">Service Fee</span>
                                <span className="font-medium">$0.00</span>
                            </div>
                            <Separator className="my-4" />
                            <div className="flex justify-between items-center text-xl font-semibold">
                                <span className="text-gray-700">Total</span>
                                <span className="text-2xl text-primary">${total.toFixed(2)}</span>
                            </div>
                        </div>
                    </div>
                </CardContent>

                <CardFooter className="flex flex-col sm:flex-row justify-between p-6 bg-gray-50 border-t gap-4">
                    <Button
                        variant="outline"
                        className="bg-blue-600 hover:bg-blue-700 flex items-center justify-center px-6 py-3 text-base font-medium w-full sm:w-auto"
                        onClick={() => navigate('/dashboard/plans')}
                    >
                        Continue Shopping
                    </Button>
                    <Button
                        onClick={handlePlaceOrder}
                        disabled={placingOrder}
                        className="px-8 py-3 text-base font-medium w-full sm:w-auto bg-primary hover:bg-primary/90"
                    >
                        {placingOrder ? (
                            <>
                                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                Placing Order...
                            </>
                        ) : (
                            'Place Order'
                        )}
                    </Button>
                </CardFooter>
            </Card>
        </div>
    );
};

export default Cart;