import React, { useState, useEffect, useCallback } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription,
} from '@/components/ui/card';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/axios';
import { Pencil, Plus, Trash, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { io } from 'socket.io-client';

const formSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    type: z.enum(['API', 'Custom']),
    country: z.string().optional(),
    apiEndpoint: z.string().optional().or(z.literal('')),
    apiKey: z.string().optional().or(z.literal('')),
    description: z.string().optional(),
    status: z.enum(['active', 'inactive']).default('active'),
});

export default function Providers() {
    const [providers, setProviders] = useState([]);
    const [isOpen, setIsOpen] = useState(false);
    const [editingProvider, setEditingProvider] = useState(null);
    const [loading, setLoading] = useState(true);
    const [hiddenProviders, setHiddenProviders] = useState(new Set());
    const [updatingVisibility, setUpdatingVisibility] = useState(new Set());
    const [syncingProviders, setSyncingProviders] = useState(new Set());
    const [syncProgress, setSyncProgress] = useState({});
    const [billionConnectSyncing, setBillionConnectSyncing] = useState(false);
    const [billionConnectSyncProgress, setBillionConnectSyncProgress] = useState({
        phase: '',
        message: '',
        progress: 0
    });
    const { toast } = useToast();
    const defaultValues = {
        name: '',
        type: 'Custom',
        country: '',
        apiEndpoint: '',
        apiKey: '',
        description: '',
        status: 'active'
    };

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues
    });

    // WebSocket connection for sync updates
    useEffect(() => {
        const socket = io(import.meta.env.VITE_API_URL || 'http://localhost:3000', {
            transports: ['websocket']
        });

        socket.on('connect', () => {
            // console.log('Connected to WebSocket');
        });

        socket.on('sync_progress', (data) => {
            setSyncProgress(prev => ({
                ...prev,
                mobimatter: data
            }));
        });

        socket.on('sync_complete', (data) => {
            setSyncingProviders(prev => {
                const newSet = new Set(prev);
                newSet.delete('mobimatter');
                return newSet;
            });
            setSyncProgress(prev => ({
                ...prev,
                mobimatter: { phase: '', message: '', progress: 0 }
            }));

            toast({
                title: 'Mobimatter Sync Complete',
                description: `Plans synced successfully. Updated: ${data.results?.updated || 0}, Created: ${data.results?.created || 0}`
            });

            // Refresh the providers list
            fetchProviders();
        });

        socket.on('sync_error', (data) => {
            setSyncingProviders(prev => {
                const newSet = new Set(prev);
                newSet.delete('mobimatter');
                return newSet;
            });
            setSyncProgress(prev => ({
                ...prev,
                mobimatter: { phase: '', message: '', progress: 0 }
            }));

            toast({
                variant: 'destructive',
                title: 'Mobimatter Sync Failed',
                description: data.message || 'Failed to sync Mobimatter plans'
            });
        });

        socket.on('billionconnect_sync_progress', (data) => {
            setBillionConnectSyncProgress(data);
        });

        socket.on('billionconnect_sync_complete', (data) => {
            setBillionConnectSyncing(false);
            setBillionConnectSyncProgress({ phase: '', message: '', progress: 0 });

            toast({
                title: 'BillionConnect Sync Complete',
                description: `BillionConnect plans synced successfully. Updated: ${data.results?.updated || 0}, Created: ${data.results?.created || 0}`
            });

            // Refresh the providers list
            fetchProviders();
        });

        socket.on('billionconnect_sync_error', (data) => {
            setBillionConnectSyncing(false);
            setBillionConnectSyncProgress({ phase: '', message: '', progress: 0 });

            toast({
                variant: 'destructive',
                title: 'BillionConnect Sync Failed',
                description: data.message || 'Failed to sync BillionConnect plans'
            });
        });

        return () => {
            socket.disconnect();
        };
    }, [toast]);

    const checkProviderPlanVisibility = useCallback(async (providersList) => {
        try {
            const hiddenProviderIds = new Set();

            // Check each provider's plan visibility
            for (const provider of providersList) {
                const response = await api.get(`/api/esim-plans?provider=${provider.id}&status=visible&limit=1`);
                // If no visible plans found, this provider's plans are hidden
                if (response.data.plans && response.data.plans.length === 0) {
                    hiddenProviderIds.add(provider.id);
                }
            }

            setHiddenProviders(hiddenProviderIds);
        } catch (error) {
            console.error('Error checking provider plan visibility:', error);
            // Don't show error toast for this as it's not critical
        }
    }, []);

    const fetchProviders = useCallback(async () => {
        try {
            const response = await api.get('/api/providers/with-active-plan-counts');
            setProviders(response.data);

            // Check which providers have all their plans hidden
            await checkProviderPlanVisibility(response.data);
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to fetch providers',
                variant: 'destructive',
            });
        } finally {
            setLoading(false);
        }
    }, [toast, checkProviderPlanVisibility]);

    useEffect(() => {
        fetchProviders();
    }, [fetchProviders]);

    const onSubmit = async (data) => {
        try {
            if (editingProvider) {
                await api.put(`/api/providers/${editingProvider.id}`, data);
                toast({
                    title: 'Success',
                    description: 'Provider updated successfully',
                });
            } else {
                await api.post('/api/providers', data);
                toast({
                    title: 'Success',
                    description: 'Provider created successfully',
                });
            }
            setIsOpen(false);
            setEditingProvider(null);
            form.reset(defaultValues);
            fetchProviders();
        } catch (error) {
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to save provider',
                variant: 'destructive',
            });
        }
    };

    const handleEdit = (provider) => {
        setEditingProvider(provider);
        form.reset(provider);
        setIsOpen(true);
    };

    const handleDelete = async (id) => {
        if (!window.confirm('Are you sure you want to delete this provider?')) {
            return;
        }

        try {
            await api.delete(`/api/providers/${id}`);
            toast({
                title: 'Success',
                description: 'Provider deleted successfully',
            });
            fetchProviders();
        } catch (error) {
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to delete provider',
                variant: 'destructive',
            });
        }
    };

    const handleAddNew = () => {
        setEditingProvider(null);
        form.reset(defaultValues);
        setIsOpen(true);
    };

    const handleToggleProviderPlansVisibility = async (providerId, currentlyHidden) => {
        setUpdatingVisibility(prev => new Set(prev).add(providerId));

        try {
            // Update all plans for this provider
            const action = currentlyHidden ? 'show' : 'hide';
            const newStatus = currentlyHidden ? 'visible' : 'hidden';

            await api.put(`/api/esim-plans/provider/${providerId}/visibility`, {
                status: newStatus
            });

            // Update local state
            if (currentlyHidden) {
                setHiddenProviders(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(providerId);
                    return newSet;
                });
            } else {
                setHiddenProviders(prev => new Set(prev).add(providerId));
            }

            toast({
                title: 'Success',
                description: `All plans for this provider have been ${action === 'hide' ? 'hidden' : 'shown'}`,
            });
        } catch (error) {
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to update plan visibility',
                variant: 'destructive',
            });
        } finally {
            setUpdatingVisibility(prev => {
                const newSet = new Set(prev);
                newSet.delete(providerId);
                return newSet;
            });
        }
    };

    const handleSync = async (providerName) => {
        try {
            setSyncingProviders(prev => new Set(prev).add(providerName.toLowerCase()));

            if (providerName.toLowerCase() === 'billionconnect') {
                setBillionConnectSyncing(true);
                await api.post('/api/esim-plans/sync/billionconnect');
                toast({
                    title: 'BillionConnect Sync Started',
                    description: 'BillionConnect plans sync operation started in background. You will be notified when complete.'
                });
            } else if (providerName.toLowerCase() === 'mobimatter') {
                await api.post('/api/esim-plans/sync/mobimatter');
                toast({
                    title: 'Mobimatter Sync Started',
                    description: 'Mobimatter plans sync operation started in background. You will be notified when complete.'
                });
            }
        } catch (error) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.response?.data?.message || 'Failed to start sync operation'
            });
            setSyncingProviders(prev => {
                const newSet = new Set(prev);
                newSet.delete(providerName.toLowerCase());
                return newSet;
            });
            if (providerName.toLowerCase() === 'billionconnect') {
                setBillionConnectSyncing(false);
            }
        }
    };

    useEffect(() => {
        if (!isOpen) {
            setEditingProvider(null);
            form.reset(defaultValues);
        }
    }, [isOpen, form]);

    return (
        <div className="h-full flex flex-col gap-6 p-6">
            <Card className="p-4">
                <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 rounded-t-lg">
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle className="text-white">Providers</CardTitle>
                            <CardDescription className="text-white/80">
                                Manage and track all providers
                            </CardDescription>
                        </div>
                        <Dialog open={isOpen} onOpenChange={setIsOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAddNew} className="bg-purple-500 text-white">
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add Provider
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>
                                        {editingProvider ? 'Edit Provider' : 'Add New Provider'}
                                    </DialogTitle>
                                </DialogHeader>
                                <Form {...form}>
                                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                        <FormField
                                            control={form.control}
                                            name="name"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Name</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="Enter provider name" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Type Field */}
                                        <FormField
                                            control={form.control}
                                            name="type"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Type</FormLabel>
                                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select provider type" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            <SelectItem value="API">API</SelectItem>
                                                            <SelectItem value="Custom">Custom</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Country Field */}
                                        <FormField
                                            control={form.control}
                                            name="country"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Country</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="Enter country" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Conditional Fields for API Type */}
                                        {form.watch('type') === 'API' && (
                                            <>
                                                {/* API Endpoint Field */}
                                                <FormField
                                                    control={form.control}
                                                    name="apiEndpoint"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>API Endpoint</FormLabel>
                                                            <FormControl>
                                                                <Input {...field} placeholder="Enter API endpoint" />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                {/* API Key Field */}
                                                <FormField
                                                    control={form.control}
                                                    name="apiKey"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>API Key</FormLabel>
                                                            <FormControl>
                                                                <Input type="password" {...field} placeholder="Enter API key" />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            </>
                                        )}

                                        {/* Description Field */}
                                        <FormField
                                            control={form.control}
                                            name="description"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Description</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="Enter description" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Status Field */}
                                        <FormField
                                            control={form.control}
                                            name="status"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Status</FormLabel>
                                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select status" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            <SelectItem value="active">Active</SelectItem>
                                                            <SelectItem value="inactive">Inactive</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Submit Button */}
                                        <Button type="submit" className="w-full">
                                            {editingProvider ? 'Update Provider' : 'Add Provider'}
                                        </Button>
                                    </form>
                                </Form>
                            </DialogContent>
                        </Dialog>
                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                <TableHead>Name</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Country</TableHead>
                                <TableHead>Active Plans</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead  className="text-center">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {providers.map((provider) => (
                                <TableRow key={provider.id}>
                                    <TableCell>{provider.name}</TableCell>
                                    <TableCell>
                                        <Badge variant={provider.type === 'API' ? 'default' : 'secondary'}>
                                            {provider.type}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>{provider.country || '-'}</TableCell>
                                    <TableCell className="text-center"><Badge variant={provider.activePlansCount > 0 ? 'success' : 'destructive'}>{provider.activePlansCount ?? 0} </Badge></TableCell>
                                    <TableCell>
                                        <Badge variant={provider.status === 'active' ? 'success' : 'destructive'}>
                                            {provider.status}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleEdit(provider)}
                                                className="bg-blue-700 text-white hover:bg-blue-600"
                                            >
                                                <Pencil className="w-4 h-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleDelete(provider.id)}
                                                disabled={provider?.type === 'API'}
                                                className="bg-blue-700 text-white hover:bg-blue-600"
                                            >
                                                <Trash className="w-4 h-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleToggleProviderPlansVisibility(
                                                    provider.id,
                                                    hiddenProviders.has(provider.id)
                                                )}
                                                disabled={updatingVisibility.has(provider.id)}
                                            >
                                                {hiddenProviders.has(provider.id) ? (
                                                    <Eye className="w-4 h-4 mr-2" />
                                                ) : (
                                                    <EyeOff className="w-4 h-4 mr-2" />
                                                )}
                                                {hiddenProviders.has(provider.id) ? 'Show Plans' : 'Hide Plans'}
                                            </Button>
                                            {provider.type === 'API' && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleSync(provider.name)}
                                                    disabled={syncingProviders.has(provider.name.toLowerCase())}
                                                    className="bg-blue-700 text-white hover:bg-blue-600"
                                                >
                                                    <RefreshCw
                                                        className={`w-4 h-4 mr-2 ${
                                                            syncingProviders.has(provider.name.toLowerCase()) ? "animate-spin" : ""
                                                        }`}
                                                    />
                                                    Sync
                                                </Button>
                                            )}
                                        </div>
                                        {/* Sync Progress Indicator */}
                                        {(provider.name.toLowerCase() === 'mobimatter' && syncProgress.mobimatter?.message) && (
                                            <div className="mt-2 text-xs text-gray-600">
                                                <div className="flex items-center gap-2">
                                                    <div className="w-32 bg-gray-200 rounded-full h-1.5">
                                                        <div
                                                            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                                                            style={{ width: `${syncProgress.mobimatter.progress}%` }}
                                                        ></div>
                                                    </div>
                                                    <span>{Math.round(syncProgress.mobimatter.progress)}%</span>
                                                </div>
                                                <p className="mt-1">{syncProgress.mobimatter.message}</p>
                                            </div>
                                        )}
                                        {(provider.name.toLowerCase() === 'billionconnect' && billionConnectSyncProgress.message) && (
                                            <div className="mt-2 text-xs text-gray-600">
                                                <div className="flex items-center gap-2">
                                                    <div className="w-32 bg-gray-200 rounded-full h-1.5">
                                                        <div
                                                            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                                                            style={{ width: `${billionConnectSyncProgress.progress}%` }}
                                                        ></div>
                                                    </div>
                                                    <span>{Math.round(billionConnectSyncProgress.progress)}%</span>
                                                </div>
                                                <p className="mt-1">{billionConnectSyncProgress.message}</p>
                                            </div>
                                        )}
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    );
}