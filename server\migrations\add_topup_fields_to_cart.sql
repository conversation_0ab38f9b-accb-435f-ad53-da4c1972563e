-- Migration to add top-up fields to cart table
-- These fields will store top-up context for cart items

ALTER TABLE cart 
ADD COLUMN isTopUp BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN parentOrderId VARCHAR(255) NULL 
COMMENT 'External order ID of the parent order for top-up orders';

-- Add index for better query performance
CREATE INDEX idx_cart_is_topup ON cart(isTopUp);
CREATE INDEX idx_cart_parent_order_id ON cart(parentOrderId);
