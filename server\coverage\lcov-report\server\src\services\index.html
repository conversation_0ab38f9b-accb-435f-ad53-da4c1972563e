
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for server/src/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> server/src/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.61% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>34/736</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0.98% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>5/506</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.09% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/122</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>34/717</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="billionconnect.service.js"><a href="billionconnect.service.js.html">billionconnect.service.js</a></td>
	<td data-value="2.81" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.81" class="pct low">2.81%</td>
	<td data-value="249" class="abs low">7/249</td>
	<td data-value="2.73" class="pct low">2.73%</td>
	<td data-value="146" class="abs low">4/146</td>
	<td data-value="3.03" class="pct low">3.03%</td>
	<td data-value="33" class="abs low">1/33</td>
	<td data-value="2.84" class="pct low">2.84%</td>
	<td data-value="246" class="abs low">7/246</td>
	</tr>

<tr>
	<td class="file low" data-value="cache.service.js"><a href="cache.service.js.html">cache.service.js</a></td>
	<td data-value="40" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 40%"></div><div class="cover-empty" style="width: 60%"></div></div>
	</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="10" class="abs low">4/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="7" class="abs low">1/7</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="10" class="abs low">4/10</td>
	</tr>

<tr>
	<td class="file low" data-value="cron.service.js"><a href="cron.service.js.html">cron.service.js</a></td>
	<td data-value="16" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16" class="pct low">16%</td>
	<td data-value="50" class="abs low">8/50</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="5" class="abs low">1/5</td>
	<td data-value="16" class="pct low">16%</td>
	<td data-value="50" class="abs low">8/50</td>
	</tr>

<tr>
	<td class="file low" data-value="mobimatter.service.js"><a href="mobimatter.service.js.html">mobimatter.service.js</a></td>
	<td data-value="3.97" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.97" class="pct low">3.97%</td>
	<td data-value="176" class="abs low">7/176</td>
	<td data-value="0.72" class="pct low">0.72%</td>
	<td data-value="137" class="abs low">1/137</td>
	<td data-value="4.54" class="pct low">4.54%</td>
	<td data-value="22" class="abs low">1/22</td>
	<td data-value="4.02" class="pct low">4.02%</td>
	<td data-value="174" class="abs low">7/174</td>
	</tr>

<tr>
	<td class="file low" data-value="provider.factory.js"><a href="provider.factory.js.html">provider.factory.js</a></td>
	<td data-value="3.18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.18" class="pct low">3.18%</td>
	<td data-value="251" class="abs low">8/251</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="216" class="abs low">0/216</td>
	<td data-value="1.81" class="pct low">1.81%</td>
	<td data-value="55" class="abs low">1/55</td>
	<td data-value="3.37" class="pct low">3.37%</td>
	<td data-value="237" class="abs low">8/237</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-01T09:28:36.077Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    