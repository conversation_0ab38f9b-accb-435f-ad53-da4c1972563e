import { useState, useEffect, useMemo, memo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../../lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
    ArrowLeft, 
    Globe, 
    Loader2, 
    Clock, 
    Database,
    Wifi,
    ShoppingCart
} from "lucide-react";

// Color scheme helper function
const getColorScheme = (planType) => {
    switch (planType) {
        case 'Fixed':
            return {
                bg: 'bg-blue-600',
                light: 'bg-blue-100',
                text: 'text-blue-600',
                border: 'border-blue-200'
            };
        case 'Unlimited':
            return {
                bg: 'bg-green-600',
                light: 'bg-green-100',
                text: 'text-green-600',
                border: 'border-green-200'
            };
        case 'Custom':
            return {
                bg: 'bg-purple-600',
                light: 'bg-purple-100',
                text: 'text-purple-600',
                border: 'border-purple-200'
            };
        default:
            return {
                bg: 'bg-gray-600',
                light: 'bg-gray-100',
                text: 'text-gray-600',
                border: 'border-gray-200'
            };
    }
};

// Memoized PlanCard component
const PlanCard = memo(({ plan, onViewDetails }) => {
    const colors = useMemo(() => getColorScheme(plan.planType), [plan.planType]);

    const formattedDataPlan = useMemo(() => {
        if (plan.planType === 'Unlimited') return 'Unlimited Data';
        if (plan.planType === 'Custom') return plan.customPlanData;
        if (plan.planType === 'Fixed') return `${plan.planData} ${plan.planDataUnit}`;
        return 'Data plan not specified';
    }, [plan.planType, plan.customPlanData, plan.planData, plan.planDataUnit]);

    const handleViewDetails = () => {
        onViewDetails(plan);
    };

    return (
        <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 shadow-md bg-white/90 backdrop-blur-sm">
            <div className={`h-2 ${colors.bg} rounded-t-lg`}></div>
            
            <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                    <div>
                        <Badge variant="outline" className={`mb-2 ${colors.light} ${colors.text} border-0`}>
                            {plan.planType}
                        </Badge>
                        <Badge variant="outline" className={`mb-2 ml-2 ${colors.light} ${colors.text} border-0`}>
                            Top-Up
                        </Badge>
                        <CardTitle className="text-xl text-gray-800 font-bold">{plan.name}</CardTitle>
                    </div>
                    <div className={`p-2 rounded-lg ${colors.light} transition-all group-hover:scale-110`}>
                        <Globe className={`w-6 h-6 ${colors.text}`} />
                    </div>
                </div>
            </CardHeader>

            <CardContent className="space-y-4">
                {/* Data Plan */}
                <div className="flex items-center gap-2">
                    <Database className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">{formattedDataPlan}</span>
                </div>

                {/* Validity */}
                <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">{plan.validityDays} days validity</span>
                </div>

                {/* Network */}
                <div className="flex items-center gap-2">
                    <Wifi className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">{plan.networkName}</span>
                </div>

                {/* Price */}
                <div className="flex items-center justify-between pt-2 border-t">
                    <div>
                        <span className="text-2xl font-bold text-gray-900">
                            ${plan.displayPrice || plan.sellingPrice}
                        </span>
                    </div>
                </div>

                <div className="mt-auto">
                    <Button
                        className={`w-full ${colors.bg} hover:bg-opacity-90 transition-all`}
                        onClick={handleViewDetails}
                    >
                        View Details
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
});

PlanCard.displayName = 'PlanCard';

const TopUpPlans = () => {
    const { orderId } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);
    const [originalOrder, setOriginalOrder] = useState(null);
    const [topUpPlans, setTopUpPlans] = useState([]);

    useEffect(() => {
        const fetchTopUpPlans = async () => {
            try {
                setLoading(true);
                const response = await api.get(`/api/partner/top-up-plans/${orderId}`);
                
                if (response.data.success) {
                    setOriginalOrder(response.data.data.originalOrder);
                    setTopUpPlans(response.data.data.topUpPlans);
                } else {
                    toast({
                        variant: "destructive",
                        title: "Error",
                        description: response.data.message || "Failed to fetch top-up plans",
                    });
                }
            } catch (error) {
                console.error('Error fetching top-up plans:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: error.response?.data?.message || "Failed to fetch top-up plans",
                });
            } finally {
                setLoading(false);
            }
        };

        if (orderId) {
            fetchTopUpPlans();
        }
    }, [orderId, toast]);

    const handleViewDetails = (plan) => {
        // Navigate to plan details page with top-up context
        navigate(`/dashboard/plan-details/${plan.id}?topUp=true&parentOrderId=${orderId}`);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                <div className="text-center">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                    <p className="text-gray-600">Loading top-up plans...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div className="container mx-auto p-6">
                {/* Header Section */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            onClick={() => navigate(`/dashboard/order-details/${orderId}`)}
                            className="flex items-center gap-2 bg-black text-white border-gray-200 shadow-sm"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Order
                        </Button>
                        <div className="hidden md:block">
                            <h1 className="text-2xl font-bold text-gray-900">Top-Up Plans</h1>
                            <p className="text-gray-600">
                                {originalOrder && `Add data to your ${originalOrder.planName} (${originalOrder.network})`}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Plans Grid */}
                {topUpPlans.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {topUpPlans.map((plan) => (
                            <PlanCard
                                key={plan.id}
                                plan={plan}
                                onViewDetails={handleViewDetails}
                            />
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-700 mb-2">No Top-Up Plans Available</h3>
                        <p className="text-gray-500">
                            No compatible top-up plans found for this network.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TopUpPlans;
