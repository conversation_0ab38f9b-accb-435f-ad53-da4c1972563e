import { useState, useEffect, useMemo, memo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../../lib/axios';
import { useToast } from '@/components/ui/use-toast';
import {
    Card,
    CardHeader,
    CardTitle,
    CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
    ArrowLeft,
    Globe,
    Loader2,
    Clock,
    Database,
    Wifi,
    ShoppingCart,
    MapPin
} from "lucide-react";

// Color scheme helper function
const getColorScheme = (planType) => {
    switch (planType) {
        case 'Fixed':
            return {
                bg: 'bg-blue-600',
                light: 'bg-blue-100',
                text: 'text-blue-600',
                border: 'border-blue-200'
            };
        case 'Unlimited':
            return {
                bg: 'bg-green-600',
                light: 'bg-green-100',
                text: 'text-green-600',
                border: 'border-green-200'
            };
        case 'Custom':
            return {
                bg: 'bg-purple-600',
                light: 'bg-purple-100',
                text: 'text-purple-600',
                border: 'border-purple-200'
            };
        default:
            return {
                bg: 'bg-gray-600',
                light: 'bg-gray-100',
                text: 'text-gray-600',
                border: 'border-gray-200'
            };
    }
};

// Memoized PlanCard component
const PlanCard = memo(({ plan, onViewDetails }) => {
    const colors = useMemo(() => getColorScheme(plan.planType), [plan.planType]);

    const formattedDataPlan = useMemo(() => {
        if (plan.planType === 'Unlimited') return 'Unlimited Data';
        if (plan.planType === 'Custom') return plan.customPlanData;
        if (plan.planType === 'Fixed') return `${plan.planData} ${plan.planDataUnit}`;
        return 'Data plan not specified';
    }, [plan.planType, plan.customPlanData, plan.planData, plan.planDataUnit]);

    // Memoize price processing - same as PlanDetails page
    const displayPrice = useMemo(() => {
        return plan.displayPrice || plan.sellingPrice;
    }, [plan.displayPrice, plan.sellingPrice]);

    // Memoize regions processing
    const regions = useMemo(() => {
        return typeof plan.region === 'string'
            ? plan.region.split(',').map(r => r.trim())
            : Array.isArray(plan.region)
                ? plan.region
                : [];
    }, [plan.region]);

    // Memoize supported countries
    const supportedCountries = useMemo(() => {
        return plan.provider?.name === 'Mobimatter'
            ? plan.providerMetadata?.originalData?.supportedCountries || []
            : plan.countries || [];
    }, [plan.provider?.name, plan.providerMetadata?.originalData?.supportedCountries, plan.countries]);

    const handleViewDetails = () => {
        onViewDetails(plan);
    };

    // Debug logging
    console.log('PlanCard data:', {
        id: plan.id,
        name: plan.name,
        displayPrice,
        sellingPrice: plan.sellingPrice,
        countries: plan.countries,
        supportedCountries
    });

    return (
        <Card className="flex flex-col h-full transition-all hover:shadow-lg border border-gray-100 hover:border-blue-200 rounded-xl overflow-hidden group relative">
            <div className={`h-2 w-full ${colors.bg}`} />

            <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                    <div>
                        <Badge variant="outline" className={`mb-2 ${colors.light} ${colors.text} border-0`}>
                            {plan.planType}
                        </Badge>
                        <Badge variant="outline" className={`mb-2 ${colors.light} ${colors.text} border-0`}>
                            Top-Up
                        </Badge>
                        <CardTitle className="text-xl text-gray-800 font-bold">{plan.name}</CardTitle>
                    </div>
                    <div className={`p-2 rounded-lg ${colors.light} transition-all group-hover:scale-110`}>
                        <Globe className={`w-6 h-6 ${colors.text}`} />
                    </div>
                </div>
            </CardHeader>

            <CardContent className="flex-1 flex flex-col pt-0">
                <div className="flex items-baseline mb-6">
                    <span className="text-3xl font-bold">${displayPrice}</span>
                    <span className="text-gray-500 ml-2">/ {plan.validityDays} Day{plan.validityDays > 1 ? 's' : ''}</span>
                </div>

                <div className="space-y-4 mb-6">
                    <div className="p-3 rounded-lg bg-gray-50 flex items-center gap-3 group-hover:shadow-sm transition-all">
                        <div className={`p-2 rounded-full ${colors.light}`}>
                            <Wifi className={`w-4 h-4 ${colors.text}`} />
                        </div>
                        <div>
                            <span className="text-sm text-gray-700">Data Plan</span>
                            <p className={`font-medium ${colors.text}`}>
                                {formattedDataPlan}
                            </p>
                        </div>
                    </div>

                    <div className="flex flex-col gap-4">
                        <div className="flex items-center gap-3">
                            <div className="bg-gray-100 p-1.5 rounded-full">
                                <Clock className="w-4 h-4 text-gray-600" />
                            </div>
                            <span className="text-sm">Valid for <span className="font-medium">{plan.validityDays} Day{plan.validityDays > 1 ? 's' : ''}</span></span>
                        </div>

                        <div className="flex items-start gap-3">
                            <div className="bg-gray-100 p-1.5 rounded-full">
                                <Globe className="w-4 h-4 text-gray-600" />
                            </div>
                            <div>
                                <span className="text-sm">Region coverage</span>
                                <div className="flex flex-wrap gap-1 mt-1">
                                    {regions.length > 0 ? (
                                        <>
                                            {regions.slice(0, 2).map((region, index) => (
                                                <span key={`${region}-${index}`} className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">
                                                    {region}
                                                </span>
                                            ))}
                                            {regions.length > 2 && (
                                                <span className="text-xs text-gray-500 px-2 py-0.5 rounded-full bg-gray-50">
                                                    +{regions.length - 2} more
                                                </span>
                                            )}
                                        </>
                                    ) : (
                                        <span className="text-sm text-gray-600">No region specified</span>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-start gap-3">
                            <div className="bg-gray-100 p-1.5 rounded-full">
                                <MapPin className="w-4 h-4 text-gray-600" />
                            </div>
                            <div>
                                <span className="text-sm">Available in</span>
                                <div className="mt-1 flex flex-wrap gap-2">
                                    {supportedCountries.slice(0, 2).map((country, index) => {
                                        // Handle both object and string formats
                                        const countryCode = typeof country === 'string' ? country : country.id;
                                        const countryName = typeof country === 'string' ? country : country.name;
                                        // For local plans, use the country.id directly, for Mobimatter use the country code
                                        const flagUrl = typeof country === 'string'
                                            ? `https://flagcdn.com/w20/${country.toLowerCase()}.png`
                                            : `https://flagcdn.com/w20/${countryCode.toLowerCase()}.png`;

                                        return (
                                            <div
                                                key={`plan-${plan.id}-country-${countryCode}-idx-${index}`}
                                                className="inline-flex items-center gap-1 bg-gray-50 px-2 py-1 rounded-md hover:bg-gray-100 transition-all cursor-pointer"
                                            >
                                                <img
                                                    src={flagUrl}
                                                    alt={`${countryName} flag`}
                                                    className="w-4 h-3 object-cover rounded-sm"
                                                    title={countryName}
                                                    onError={(e) => {
                                                        e.target.onerror = null;
                                                        e.target.src = 'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/No_flag.svg/32px-No_flag.svg.png';
                                                    }}
                                                />
                                                <span className="text-xs font-medium">{countryName}</span>
                                            </div>
                                        );
                                    })}
                                    {supportedCountries.length > 2 && (
                                        <span className="text-xs bg-gray-50 px-2 py-1 rounded-md text-gray-500 hover:bg-gray-100 transition-all cursor-pointer">
                                            +{supportedCountries.length - 2} more
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mt-auto grid grid-cols-1 gap-2">
                    <Button
                        className={`${colors.bg} hover:bg-opacity-90 transition-all`}
                        onClick={handleViewDetails}
                    >
                        View Details
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
});

PlanCard.displayName = 'PlanCard';

const TopUpPlans = () => {
    const { orderId } = useParams();
    const navigate = useNavigate();
    const { toast } = useToast();
    const [loading, setLoading] = useState(true);
    const [originalOrder, setOriginalOrder] = useState(null);
    const [topUpPlans, setTopUpPlans] = useState([]);

    useEffect(() => {
        const fetchTopUpPlans = async () => {
            try {
                setLoading(true);
                const response = await api.get(`/api/orders/${orderId}/top-up-plans`);
                
                if (response.data.success) {
                    console.log('TopUp API Response:', response.data.data);
                    console.log('TopUp Plans:', response.data.data.topUpPlans);
                    setOriginalOrder(response.data.data.originalOrder);
                    setTopUpPlans(response.data.data.topUpPlans);
                } else {
                    toast({
                        variant: "destructive",
                        title: "Error",
                        description: response.data.message || "Failed to fetch top-up plans",
                    });
                }
            } catch (error) {
                console.error('Error fetching top-up plans:', error);
                toast({
                    variant: "destructive",
                    title: "Error",
                    description: error.response?.data?.message || "Failed to fetch top-up plans",
                });
            } finally {
                setLoading(false);
            }
        };

        if (orderId) {
            fetchTopUpPlans();
        }
    }, [orderId, toast]);

    const handleViewDetails = (plan) => {
        // Navigate to plan details page with top-up context
        navigate(`/dashboard/plans/${plan.id}?topUp=true&parentOrderId=${orderId}`);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
                <div className="text-center">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                    <p className="text-gray-600">Loading top-up plans...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            <div className="container mx-auto p-6">
                {/* Header Section */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            onClick={() => navigate(`/dashboard/orders/${orderId}`)}
                            className="flex items-center gap-2 bg-black text-white border-gray-200 shadow-sm"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to Order
                        </Button>
                        <div className="hidden md:block">
                            <h1 className="text-2xl font-bold text-gray-900">Top-Up Plans</h1>
                            <p className="text-gray-600">
                                {originalOrder && `Add data to your ${originalOrder.planName} (${originalOrder.network})`}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Plans Grid */}
                {topUpPlans.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {topUpPlans.map((plan) => (
                            <PlanCard
                                key={plan.id}
                                plan={plan}
                                onViewDetails={handleViewDetails}
                            />
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-gray-700 mb-2">No Top-Up Plans Available</h3>
                        <p className="text-gray-500">
                            No compatible top-up plans found for this network.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TopUpPlans;
