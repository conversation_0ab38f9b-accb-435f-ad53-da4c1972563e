const { User, Order, EsimPlan, Provider, Country } = require('../models');
const { generateApi<PERSON>ey, hash<PERSON><PERSON><PERSON><PERSON>, mask<PERSON>pi<PERSON>ey } = require('../utils/apiKeyGenerator');
const sequelize = require('../config/database');
const { Op } = require('sequelize');

/**
 * Get the partner's API key details
 */
exports.getApiKeyInfo = async (req, res) => {
    try {
        const userId = req.user.id;
        
        const partner = await User.findByPk(userId, {
            attributes: ['id', 'apiKey', 'apiKeyLastReset']
        });
        
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }
        
        // Format the response
        const apiKeyInfo = {
            partnerId: `partner_${partner.id.substring(0, 5)}`, // Create a partner_id prefix
            apiKey: partner.apiKey ? maskApiKey(partner.apiKey) : null,
            lastReset: partner.apiKeyLastReset
        };
        
        res.json(apiKeyInfo);
    } catch (error) {
        console.error('Error getting API key info:', error);
        res.status(500).json({ message: 'Error retrieving API key information' });
    }
};

/**
 * Generate or regenerate an API key for the partner
 */
exports.generateApiKey = async (req, res) => {
    const transaction = await sequelize.transaction();
    
    try {
        const userId = req.user.id;
        
        const partner = await User.findByPk(userId, { transaction });
        
        if (!partner) {
            await transaction.rollback();
            return res.status(404).json({ message: 'Partner not found' });
        }
        
        // Generate a new API key
        const apiKey = generateApiKey();
        const apiKeyHash = await hashApiKey(apiKey);
        
        // Update the partner record
        await partner.update({
            apiKey: apiKey, // We store the actual key temporarily for the response
            apiKeyHash,
            apiKeyLastReset: new Date()
        }, { transaction });
        
        // Log the API key regeneration
        console.log(`API key regenerated for partner ${partner.id} at ${new Date().toISOString()}`);
        
        await transaction.commit();
        
        res.json({
            message: 'API key generated successfully',
            apiKey
        });
        
        // After sending the response, remove the plain API key from the database
        // for security (we'll keep only the hash)
        await partner.update({ apiKey: null });
    } catch (error) {
        await transaction.rollback();
        console.error('Error generating API key:', error);
        res.status(500).json({ message: 'Error generating API key' });
    }
};

/**
 * Get full API key (reveal)
 */
exports.revealApiKey = async (req, res) => {
    try {
        const userId = req.user.id;
        
        const partner = await User.findByPk(userId);
        
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }
        
        if (!partner.apiKey) {
            return res.status(400).json({ 
                message: 'API key not available. Please regenerate your API key.' 
            });
        }
        
        res.json({
            apiKey: partner.apiKey
        });
    } catch (error) {
        console.error('Error revealing API key:', error);
        res.status(500).json({ message: 'Error revealing API key' });
    }
};

/**
 * Get top-up plans for a specific order
 */
exports.getTopUpPlans = async (req, res) => {
    try {
        const { orderId } = req.params;
        const userId = req.user.id;

        // Get partner information for markup calculation
        const partner = await User.findByPk(userId);
        if (!partner) {
            return res.status(404).json({
                success: false,
                message: 'Partner not found'
            });
        }

        // First, get the original order to verify ownership and get network info
        const originalOrder = await Order.findOne({
            where: {
                id: orderId,
                userId: userId
            },
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [{
                    model: Provider,
                    as: 'provider'
                }]
            }]
        });

        if (!originalOrder) {
            return res.status(404).json({
                success: false,
                message: 'Order not found or access denied'
            });
        }

        // Check if the order is from Mobimatter
        if (originalOrder.plan.provider.name !== 'Mobimatter') {
            return res.status(400).json({
                success: false,
                message: 'Top-up is only available for Mobimatter orders'
            });
        }

        // Get the network from the original plan
        const originalNetwork = originalOrder.plan.networkName;

        if (!originalNetwork) {
            return res.status(400).json({
                success: false,
                message: 'Cannot determine network for top-up plans'
            });
        }

        // Get all Mobimatter addon plans with the same network
        const topUpPlans = await EsimPlan.findAll({
            where: {
                status: 'visible',
                isActive: true,
                category: 'esim_addon',
                networkName: originalNetwork
            },
            include: [
                {
                    model: Provider,
                    as: 'provider',
                    where: { name: 'Mobimatter' }
                },
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                }
            ],
            order: [['createdAt', 'DESC']]
        });

        // Apply markup-based pricing for all plans (same logic as getPartnerEsimPlans)
        const processedPlans = topUpPlans.map(plan => {
            const planData = plan.toJSON ? plan.toJSON() : plan;

            // Calculate display price based on markup
            if (!planData.sellingPrice) {
                const markup = partner.markupPercentage || 0;
                const basePrice = parseFloat(planData.buyingPrice);
                const markupAmount = (basePrice * markup) / 100;
                planData.displayPrice = (basePrice + markupAmount).toFixed(2);
            } else {
                planData.displayPrice = planData.sellingPrice;
            }

            return planData;
        });

        res.json({
            success: true,
            data: {
                originalOrder: {
                    id: originalOrder.id,
                    planName: originalOrder.plan.name,
                    network: originalNetwork
                },
                topUpPlans: processedPlans
            }
        });

    } catch (error) {
        console.error('Error fetching top-up plans:', error);
        res.status(500).json({
            success: false,
            message: 'Error retrieving top-up plans'
        });
    }
};