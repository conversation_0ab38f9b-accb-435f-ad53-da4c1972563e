const models = require('../models');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const emailService = require('../utils/emailService');
const { Op } = require('sequelize');
const providerFactory = require('../services/provider.factory');
const mobimatterService = require('../services/mobimatter.service');

// Helper function to generate QR code as data URL
const generateQRCode = async (text) => {
    try {
        return await QRCode.toDataURL(text, {
            errorCorrectionLevel: 'H',
            type: 'image/png',
            margin: 2,
            width: 400,
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            rendererOpts: {
                quality: 1
            }
        });
    } catch (err) {
        console.error('Error generating QR code:', err);
        return null;
    }
};

// Helper function to parse LPA string and extract components
const parseLpaString = (lpaString) => {
    try {
        if (!lpaString || !lpaString.startsWith('LPA:')) {
            return null;
        }

        // LPA format: LPA:1$<SMDP_ADDRESS>$<ACTIVATION_CODE>
        const parts = lpaString.split('$');
        if (parts.length >= 3) {
            return {
                smdpAddress: parts[1],
                activationCode: parts[2],
                lpaString: lpaString
            };
        }

        return null;
    } catch (error) {
        console.error('Error parsing LPA string:', error);
        return null;
    }
};

// Helper function to check stock and notify admins
const checkStockAndNotify = async (planId, t) => {
    try {
        // Get plan details with current stock count and provider info
        const plan = await models.EsimPlan.findByPk(planId, {
            include: [
                {
                    model: models.EsimStock,
                    as: 'stocks',
                    where: { status: 'available' },
                    required: false
                },
                {
                    model: models.Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ],
            transaction: t
        });

        if (!plan) {
            return;
        }

        const currentStock = plan.stocks?.length || 0;
        
        // Skip low stock notification for API providers (Mobimatter, Billionconnect)
        if (plan.provider?.name === 'Mobimatter' ||
            plan.provider?.name === 'Billionconnect' ||
            plan.provider?.type === 'API') {
            console.log(`Skipping low stock notification for ${plan.provider?.name} plan (API provider)`);
            return;
        }
        
        // Check if stock is below threshold for non-Mobimatter plans
        if (currentStock < plan.stockThreshold) {
            // Get all admin users
            const admins = await models.User.findAll({
                where: { role: 'admin', isActive: true },
                attributes: ['email']
            });

            const adminEmails = admins.map(admin => admin.email);
            
            // Send notification
            await emailService.sendLowStockEmail(adminEmails, plan, currentStock);
        }
    } catch (error) {
        console.error('Error in stock check and notification:', error);
        // Don't throw error as this is a notification service
    }
};

// Create a new order from cart items
exports.createOrder = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        console.log('Starting order creation process...');
        const userId = req.user.id;
        const { items } = req.body;
        console.log('User ID:', userId);
        console.log('Items received:', items);

        // Get partner for markup calculation
        const partner = await models.User.findByPk(userId, { transaction: t });
        if (!partner) {
            console.log('Partner not found for userId:', userId);
            await t.rollback();
            return res.status(404).json({ message: 'Partner not found' });
        }
        console.log('Partner found:', partner.id);

        // Get user details for the order
        const user = await models.User.findByPk(userId);
        if (!user) {
            throw new Error('User not found');
        }

        // Get cart items with full plan details
        const cartItems = await models.Cart.findAll({
            where: { userId },
            include: [{
                model: models.EsimPlan,
                as: 'EsimPlan',
                include: [{
                    model: models.Provider,
                    as: 'provider'
                }]
            }],
            transaction: t
        });

        console.log('Cart items found:', cartItems.length);
        if (!cartItems.length) {
            console.log('Cart is empty for userId:', userId);
            await t.rollback();
            return res.status(400).json({ message: 'Cart is empty' });
        }

        // Calculate total order amount and validate prices
        let totalAmount = 0;
        const processedItems = cartItems.map(item => {
            const cartItem = item.toJSON();
            let sellingPrice = parseFloat(cartItem.EsimPlan.sellingPrice);
            
            if (!sellingPrice && partner.markupPercentage) {
                const markup = 1 + (partner.markupPercentage / 100);
                sellingPrice = Number((cartItem.EsimPlan.buyingPrice * markup).toFixed(2));
            }

            if (!sellingPrice || sellingPrice <= 0) {
                throw new Error(`Invalid price for plan: ${cartItem.EsimPlan.name} (ID: ${cartItem.EsimPlan.id})`);
            }

            totalAmount += sellingPrice;
            return { ...cartItem, calculatedPrice: sellingPrice };
        });
        console.log('Total order amount calculated:', totalAmount);

        // Check wallet balance
        const wallet = await models.Wallet.findOne({
            where: { userId },
            transaction: t,
            lock: true
        });

        if (!wallet || parseFloat(wallet.balance) < totalAmount) {
            console.log('Insufficient balance. Required:', totalAmount, 'Available:', wallet?.balance);
            await t.rollback();
            return res.status(400).json({ 
                message: 'Insufficient wallet balance',
                required: totalAmount,
                available: parseFloat(wallet?.balance || 0)
            });
        }

        // Process orders
        const orders = [];
        const usedStockIds = [];
        
        for (const cartItem of processedItems) {
            const plan = cartItem.EsimPlan;
            const provider = plan.provider;

            try {
                let orderData = null;
                let stockData = null;

                // Handle external provider orders (API-based providers)
                if (provider && provider.type === 'API') {
                    const providerService = providerFactory.getProvider(provider.name);
                    
                    console.log('Creating external order for plan:', {
                        planId: plan.id,
                        externalProductId: plan.externalProductId,
                        externalSkuId: plan.externalSkuId
                    });

                    // Create initial order record with pending status
                    orderData = await models.Order.create({
                        userId,
                        esimPlanId: plan.id,
                        esimStockId: null,
                        quantity: cartItem.quantity,
                        orderTotal: cartItem.calculatedPrice,
                        startDate: items.find(i => i.id === cartItem.id)?.startDate || null,
                        status: 'pending',
                        walletAuthTransactionId: uuidv4()
                    }, { transaction: t });

                    // Step 1: Create external order
                    const orderPayload = {
                        productId: plan.externalProductId,
                        skuId: plan.externalSkuId,
                        quantity: cartItem.quantity,
                        customerReference: `order_${userId}_${Date.now()}`,
                        email: user.email,
                        totalAmount: cartItem.calculatedPrice
                    };

                    // For top-up orders, include parent order ID
                    if (cartItem.isTopUp && cartItem.parentOrderId) {
                        orderPayload.parentOrderId = cartItem.parentOrderId;
                    }

                    const externalOrder = await providerService.createOrder(orderPayload);

                    if (!externalOrder || !externalOrder.orderId) {
                        throw new Error('Invalid response from provider: Missing order ID');
                    }

                    // Different flow for BillionConnect vs Mobimatter
                    if (provider.name === 'Billionconnect') {
                        // For BillionConnect, we wait for the N009 webhook
                        await orderData.update({
                            externalOrderId: externalOrder.orderId,
                            providerResponse: externalOrder.providerResponse,
                            providerMetadata: {},
                            providerOrderStatus: 'PENDING',
                            lastProviderCheck: new Date()
                        }, { transaction: t });

                        console.log('BillionConnect order created, waiting for webhook:', {
                            orderId: orderData.id,
                            externalOrderId: externalOrder.orderId
                        });

                        // Send initial order confirmation email for BillionConnect
                        const planDetails = await models.EsimPlan.findByPk(orderData.esimPlanId, {
                            include: [{
                                model: models.Provider,
                                as: 'provider',
                                attributes: ['id', 'name', 'type', 'country']
                            }]
                        });

                        const initialOrderDetails = {
                            order: {
                                id: orderData.id,
                                externalOrderId: externalOrder.orderId
                            },
                            plan: planDetails.get({ plain: true }),
                            orderTotal: orderData.orderTotal,
                            startDate: orderData.startDate,
                            quantity: orderData.quantity,
                            walletAuthTransactionId: orderData.walletAuthTransactionId,
                            partner: {
                                id: partner.id,
                                firstName: partner.firstName,
                                lastName: partner.lastName,
                                email: partner.email
                            }
                        };

                        try {
                            await emailService.sendBillionConnectInitialOrderEmail(partner.email, initialOrderDetails);
                            console.log('BillionConnect initial order email sent to:', partner.email);
                        } catch (error) {
                            console.error('Failed to send BillionConnect initial email:', error);
                            // Don't fail the order creation due to email error
                        }

                        // Note: For BillionConnect, we don't create the stock record here
                        // Stock record will be created when we receive the N009 webhook
                        stockData = null;

                        // Skip regular email flow for BillionConnect orders
                        await t.commit();
                        console.log('BillionConnect order created successfully, waiting for webhook');

                        return res.status(201).json({
                            message: 'Orders created successfully',
                            orders: [orderData.id],
                            orderDetails: [{
                                id: orderData.id,
                                status: orderData.status,
                                orderTotal: orderData.orderTotal,
                                externalOrderId: externalOrder.orderId,
                                plan: planDetails.get({ plain: true })
                            }]
                        });
                    } else if (provider.name.toLowerCase() === 'mobimatter') {
                        // For Mobimatter, continue with existing flow
                        const orderInfo = await providerService.getOrderStatus(externalOrder.orderId);
                        
                        if (!orderInfo) {
                            throw new Error('Failed to fetch order status');
                        }

                        if (orderInfo.status === 'FAILED' || orderInfo.orderState === 'Failed') {
                            throw new Error(`External order failed: ${orderInfo.errorMessage || 'Unknown error'}`);
                        }

                        // Update order with external order details
                        await orderData.update({
                            externalOrderId: externalOrder.orderId,
                            providerResponse: orderInfo,
                            providerMetadata: {
                                activationCode: orderInfo.activationCode,
                                qrCodeUrl: orderInfo.qrCodeUrl,
                                iccid: orderInfo.iccid,
                                smdpAddress: orderInfo.smdpAddress,
                                lpaString: orderInfo.qrCodeUrl,
                                apn: orderInfo.apn,
                                providerStatus: orderInfo.orderState
                            },
                            providerOrderStatus: orderInfo.orderState,
                            lastProviderCheck: new Date()
                        }, { transaction: t });

                        // Complete the Mobimatter order
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        const completedOrder = await providerService.completeOrder(externalOrder.orderId);
                        
                        if (!completedOrder) {
                            throw new Error('No response received from order completion');
                        }

                        // Update order with completed status
                        await orderData.update({
                            status: 'completed',
                            providerResponse: completedOrder,
                            providerOrderStatus: completedOrder.orderState,
                            lastProviderCheck: new Date()
                        }, { transaction: t });

                        // Extract eSIM details from the completed order response
                        const lineItems = completedOrder.orderLineItem?.lineItemDetails || [];
                        const getLineItemValue = (name) => lineItems.find(item => item.name === name)?.value || '';

                        // Create stock record for Mobimatter
                        stockData = {
                            id: uuidv4(),
                            esimPlanId: plan.id,
                            orderId: orderData.id,
                            iccid: getLineItemValue('ICCID'),
                            smdpAddress: getLineItemValue('SMDP_ADDRESS'),
                            lpaString: getLineItemValue('LOCAL_PROFILE_ASSISTANT'),
                            accessPointName: getLineItemValue('ACCESS_POINT_NAME'),
                            activationCode: getLineItemValue('ACTIVATION_CODE'),
                            phoneNumber: getLineItemValue('PHONE_NUMBER'),
                            qrCodeUrl: getLineItemValue('QR_CODE'),
                            confCode: getLineItemValue('CONF_CODE'),
                            status: 'assigned',
                            externalStockId: completedOrder.orderId,
                            externalIccid: getLineItemValue('ICCID'),
                            providerStatus: completedOrder.orderState,
                            providerMetadata: completedOrder,
                            walletAuthTransactionId: orderData.walletAuthTransactionId
                        };
                    } else {
                        throw new Error(`Unsupported provider type: ${provider.name}`);
                    }

                    // Save stock record if we have it
                    if (stockData) {
                        const stock = await models.EsimStock.create(stockData, { transaction: t });

                        // Create stock history
                        await models.EsimPlanStockHistory.create({
                            id: uuidv4(),
                            esimPlanId: stock.esimPlanId,
                            esimStockId: stock.id,
                            iccid: stock.iccid,
                            smdpAddress: stock.smdpAddress,
                            lpaString: stock.lpaString,
                            accessPointName: stock.accessPointName,
                            activationCode: stock.activationCode,
                            phoneNumber: stock.phoneNumber,
                            orderId: orderData.id,
                            orderDate: new Date(),
                            quantity: 1,
                            status: 'assigned',
                            reason: 'External provider order',
                            createdBy: userId,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        }, { transaction: t });
                    }

                    // Get plan details with provider info
                    const planDetails = await models.EsimPlan.findByPk(orderData.esimPlanId, {
                        include: [{
                            model: models.Provider,
                            as: 'provider',
                            attributes: ['id', 'name', 'type', 'country']
                        }]
                    });

                    if (!planDetails) {
                        throw new Error('Plan details not found');
                    }

                    // Prepare order details in the format expected by email service
                    const orderDetails = {
                        order: {
                            id: orderData.id,
                            externalOrderId: stockData?.externalStockId
                        },
                        plan: planDetails.get({ plain: true }),
                        orderTotal: orderData.orderTotal,
                        startDate: orderData.startDate,
                        quantity: orderData.quantity,
                        walletAuthTransactionId: orderData.walletAuthTransactionId,
                        qrCode: stockData?.qrCodeUrl,
                        partner: {
                            id: partner.id,
                            firstName: partner.firstName,
                            lastName: partner.lastName,
                            email: partner.email
                        },
                        stock: {
                            iccid: stockData?.iccid,
                            smdpAddress: stockData?.smdpAddress,
                            lpaString: stockData?.lpaString,
                            accessPointName: stockData?.accessPointName,
                            activationCode: stockData?.activationCode,
                            phoneNumber: stockData?.phoneNumber,
                            qrCodeUrl: stockData?.qrCodeUrl,
                            confCode: stockData?.confCode
                        }
                    };

                    // Send partner notification (skip for BillionConnect as they already received initial email)
                    if (planDetails.provider?.name !== 'Billionconnect') {
                        console.log('Sending partner notification to:', partner.email);
                        try {
                            await emailService.sendPartnerOrderEmail(partner.email, orderDetails);
                            console.log('Partner notification sent successfully');
                        } catch (error) {
                            console.error('Failed to send partner notification:', error);
                            // Update order metadata with email error
                            await orderData.update({
                                providerMetadata: {
                                    ...orderData.providerMetadata,
                                    emailError: {
                                        message: error.message,
                                        timestamp: new Date().toISOString()
                                    }
                                }
                            }, { transaction: t });
                            throw new Error('Failed to send order confirmation email');
                        }
                    } else {
                        console.log('Skipping partner notification for BillionConnect order (initial email already sent)');
                    }

                    // Send admin notifications (skip for BillionConnect as they will be sent when webhook completes)
                    if (planDetails.provider?.name !== 'Billionconnect') {
                        const admins = await models.User.findAll({
                            where: {
                                role: 'admin',
                                isActive: true
                            },
                            attributes: ['email'],
                            transaction: t
                        });

                        if (admins && admins.length > 0) {
                            const adminEmails = admins.map(admin => admin.email);
                            console.log('Sending admin notifications to:', adminEmails);

                            try {
                                await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                                console.log('Admin notifications sent successfully');
                            } catch (error) {
                                console.error('Failed to send admin notifications:', error);
                                // Update order metadata with admin email error
                                await orderData.update({
                                    providerMetadata: {
                                        ...orderData.providerMetadata,
                                        adminEmailError: {
                                            message: error.message,
                                            timestamp: new Date().toISOString()
                                        }
                                    }
                                }, { transaction: t });
                                // Don't throw here, as partner notification was successful
                            }
                        }
                    } else {
                        console.log('Skipping admin notifications for BillionConnect order (will be sent when webhook completes)');
                    }

                    console.log('Email notifications sent successfully for order:', orderData.id);
                } else {
                    // Handle local stock order
                    const stock = await models.EsimStock.findOne({
                        where: {
                            esimPlanId: plan.id,
                            status: 'available'
                        },
                        transaction: t,
                        lock: true 
                    });

                    if (!stock) {
                        throw new Error(`No stock available for plan: ${plan.name}`);
                    }

                    // Create order record
                    orderData = await models.Order.create({
                        userId,
                        esimPlanId: plan.id,
                        esimStockId: stock.id,
                        quantity: cartItem.quantity,
                        orderTotal: cartItem.calculatedPrice,
                        startDate: items.find(i => i.id === cartItem.id)?.startDate || null,
                        status: 'completed',
                        walletAuthTransactionId: uuidv4(),
                        providerResponse: stock.providerMetadata || null,
                        providerMetadata: {
                            iccid: stock.iccid,
                            smdpAddress: stock.smdpAddress,
                            lpaString: stock.lpaString,
                            accessPointName: stock.accessPointName,
                            activationCode: stock.activationCode,
                            phoneNumber: stock.phoneNumber,
                            qrCodeUrl: stock.qrCodeUrl,
                            providerStatus: stock.providerStatus
                        }
                    }, { transaction: t });

                    // Create stock history
                    await models.EsimPlanStockHistory.create({
                        id: uuidv4(),
                        esimPlanId: stock.esimPlanId,
                        esimStockId: stock.id,
                        iccid: stock.iccid,
                        smdpAddress: stock.smdpAddress,
                        lpaString: stock.lpaString,
                        accessPointName: stock.accessPointName,
                        activationCode: stock.activationCode,
                        phoneNumber: stock.phoneNumber,
                        orderId: orderData.id,
                        orderDate: new Date(),
                        quantity: 1,
                        status: 'assigned',
                        reason: 'Local stock order',
                        createdBy: userId,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    }, { transaction: t });

                    // Delete used stock
                    //    await models.EsimStock.destroy({
                    //     where: { id: stock.id },
                    //     transaction: t
                    // });

                    // Update stock status instead of deleting
                    await models.EsimStock.update(
                        {
                            status: 'assigned',
                            orderId: orderData.id,
                            orderDate: new Date(),
                            updatedAt: new Date()
                        },
                        {
                            where: { id: stock.id },
                            transaction: t
                        }
                    );

                    usedStockIds.push(stock.id);
                }

                orders.push(orderData.id);
            } catch (error) {
                console.error('Error processing order item:', error);
                throw error;
            }
        }

        // Process wallet transaction
        try {
            const newBalance = parseFloat(wallet.balance) - totalAmount;
            await models.WalletTransaction.create({
                id: uuidv4(),
                walletId: wallet.id,
                type: 'debit',
                amount: totalAmount,
                balance: newBalance,
                description: `Payment for order: ${orders.join(', ')}`,
                status: 'completed',
                referenceType: 'order',
                referenceId: orders[0],
                metadata: {
                    orders,
                    totalAmount,
                    usedStockIds
                }
            }, { transaction: t });

            // Update wallet balance
            await wallet.update({
                balance: newBalance,
                updatedAt: new Date()
            }, { transaction: t });

            console.log('Wallet transaction processed');
        } catch (error) {
            console.error('Error processing wallet transaction:', error);
            
            // Try to cancel any external orders that were created
            for (const orderId of orders) {
                try {
                    const order = await models.Order.findByPk(orderId, {
                        include: [{
                            model: models.EsimPlan,
                            as: 'plan',
                            include: [{
                                model: models.Provider,
                                as: 'provider'
                            }]
                        }]
                    });

                    if (order?.plan?.provider?.type === 'API' && order.externalOrderId) {
                        const providerService = providerFactory.getProvider(order.plan.provider.name);
                        await providerService.cancelOrder(order.externalOrderId);
                    }
                } catch (cancelError) {
                    console.error(`Error cancelling order ${orderId}:`, cancelError);
                }
            }
            
            await t.rollback();
            return res.status(500).json({ 
                message: 'Error processing wallet transaction',
                error: error.message 
            });
        }

        // Clear cart
        await models.Cart.destroy({
            where: { userId },
            transaction: t
        });

        console.log('Cart cleared');

        // Check stock levels
        for (const cartItem of processedItems) {
            await checkStockAndNotify(cartItem.esimPlanId, t);
        }

        console.log('Committing transaction...');
        // Commit transaction only after all operations are successful
        await t.commit();
        console.log('Transaction committed successfully');

        // Get all admin users for notification
        const admins = await models.User.findAll({
            where: { role: 'admin', isActive: true },
            attributes: ['email']
        });

        // Send notifications for each order
        for (const orderId of orders) {
            try {
                const order = await models.Order.findOne({
                    where: { id: orderId },
                    include: [
                        {
                            model: models.EsimPlan,
                            as: 'plan',
                            include: [
                                {
                                    model: models.Provider,
                                    as: 'provider',
                                    attributes: ['id', 'name', 'type', 'country']
                                }
                            ]
                        },
                        {
                            model: models.EsimPlanStockHistory,
                            as: 'stockHistory',
                            attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'orderDate', 'quantity', 'reason']
                        }
                    ]
                });

                if (order) {
                    console.log('Processing order for notifications:', orderId);
                    console.log('Provider type:', order.plan?.provider?.name);
                    
                    // Skip Mobimatter orders as they've already been notified
                    if (order.plan?.provider?.name === 'Mobimatter') {
                        console.log('Skipping notification for Mobimatter order as it was already sent');
                        continue;
                    }

                    console.log('Provider metadata:', order.providerMetadata);

                    // Get eSIM details based on provider type
                    let esimDetails = {};
                    let qrCode = null;

                    // Process local stock order details
                    console.log('Processing local stock order details');
                    esimDetails = {
                        iccid: order.stockHistory?.iccid,
                        phoneNumber: order.stockHistory?.phoneNumber,
                        lpaString: order.stockHistory?.lpaString,
                        smdpAddress: order.stockHistory?.smdpAddress,
                        accessPointName: order.stockHistory?.accessPointName,
                        activationCode: order.stockHistory?.activationCode
                    };
                    if (esimDetails.lpaString) {
                        // Generate QR code from the LPA string if available
                        try {
                            qrCode = await generateQRCode(esimDetails.lpaString);
                            console.log('QR code generated successfully from LPA string');
                        } catch (error) {
                            console.error('Error generating QR code:', error);
                            // Try using QR_CODE value as fallback
                            const qrCodeValue = esimDetails.qrCodeUrl;
                            if (qrCodeValue) {
                                try {
                                    qrCode = await generateQRCode(qrCodeValue);
                                    console.log('QR code generated successfully from QR_CODE value');
                                } catch (fallbackError) {
                                    console.error('Error generating QR code from fallback:', fallbackError);
                                }
                            }
                        }
                    }
                    console.log('Local stock eSIM details:', esimDetails);

                    // Prepare order details for emails
                    const orderDetails = {
                        order: {
                            id: orderId,
                            externalOrderId: order.externalOrderId
                        },
                        plan: order.plan,
                        orderTotal: order.orderTotal,
                        startDate: order.startDate,
                        quantity: order.quantity,
                        walletAuthTransactionId: order.walletAuthTransactionId,
                        qrCode,
                        partner: {
                            id: userId,
                            firstName: partner.firstName,
                            lastName: partner.lastName,
                            email: partner.email
                        },
                        stock: esimDetails
                    };

                    console.log('Sending email notifications for local stock order:', orderId);
                    console.log('Partner email:', partner.email);

                    try {
                        // Send partner email with QR code
                        await emailService.sendPartnerOrderEmail(partner.email, orderDetails);
                        console.log('Partner email sent successfully');

                        // Send admin notification for all orders
                        if (admins.length > 0) {
                            const adminEmails = admins.map(admin => admin.email);
                            console.log('Sending admin notifications to:', adminEmails);
                            await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                            console.log('Admin emails sent successfully');
                        }
                    } catch (error) {
                        console.error('Error sending order notifications:', error);
                        // Log more details about the error
                        console.error('Order details:', JSON.stringify(orderDetails, null, 2));
                        console.error('Error details:', error.response?.data || error.message);
                        // Don't throw error as this is a notification service
                    }
                } else {
                    console.error('Order not found:', orderId);
                }
            } catch (error) {
                console.error(`Error sending notifications for order ${orderId}:`, error);
                // Don't throw error as this is a notification service
            }
        }

        // Return success response with order IDs and details
        return res.status(201).json({
            message: 'Orders created successfully',
            orders: orders,
            orderDetails: await Promise.all(orders.map(async (orderId) => {
                const order = await models.Order.findByPk(orderId, {
                    include: [
                        {
                            model: models.EsimPlan,
                            as: 'plan'
                        },
                        {
                            model: models.EsimPlanStockHistory,
                            as: 'stockHistory',
                            attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'orderDate', 'quantity', 'reason']
                        }
                    ]
                });
                return order;
            }))
        });

    } catch (error) {
        console.error('Error in order creation:', error);
        await t.rollback();
        return res.status(500).json({ 
            message: 'Error creating order',
            error: error.message 
        });
    }
};

// Get all orders for the current user
exports.getUserOrders = async (req, res) => {
    try {
        const userId = req.user.id;
        const orders = await models.Order.findAll({
            where: { userId },
            include: [
                {
                model: models.EsimPlan,
                as: 'plan',
                    include: [{
                        model: models.Provider,
                        as: 'provider',
                        attributes: ['id', 'name', 'type', 'country']
                    }]
                },
                {
                    model: models.EsimStock,
                    as: 'stock',
                    attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'providerMetadata']
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: ['id', 'iccid', 'smdpAddress', 'lpaString', 'accessPointName', 'activationCode', 'phoneNumber', 'status', 'orderDate', 'quantity', 'reason']
                }
            ],
            order: [['createdAt', 'DESC']]
        });

        // Format the response with QR codes
        const formattedOrders = await Promise.all(orders.map(async (order) => {
            const orderJson = order.toJSON();
            
            // Prepare eSIM details based on order type
            const esimDetails = {
                // For MobiMatter orders, use providerMetadata
                ...(orderJson.providerMetadata && {
                    iccid: orderJson.providerMetadata.iccid,
                    activationCode: orderJson.providerMetadata.activationCode,
                    qrCodeUrl: orderJson.providerMetadata.qrCodeUrl,
                    smdpAddress: orderJson.providerMetadata.smdpAddress,
                    lpaString: orderJson.providerMetadata.lpaString || orderJson.providerMetadata.qrCodeUrl,
                    accessPointName: orderJson.providerMetadata.apn,
                    phoneNumber: orderJson.providerMetadata.phoneNumber,
                    matchingId: orderJson.providerMetadata.matchingId,
                    profileState: orderJson.providerMetadata.profileState,
                    providerStatus: orderJson.providerMetadata.providerStatus
                }),
                // For local stock orders, use stock or stockHistory
                ...(orderJson.stock && {
                    iccid: orderJson.stock.iccid,
                    activationCode: orderJson.stock.activationCode,
                    smdpAddress: orderJson.stock.smdpAddress,
                    lpaString: orderJson.stock.lpaString,
                    accessPointName: orderJson.stock.accessPointName,
                    phoneNumber: orderJson.stock.phoneNumber,
                    providerMetadata: orderJson.stock.providerMetadata
                }),
                // Fallback to stockHistory if stock is not available
                ...(!orderJson.stock && orderJson.stockHistory && {
                    iccid: orderJson.stockHistory.iccid,
                    activationCode: orderJson.stockHistory.activationCode,
                    smdpAddress: orderJson.stockHistory.smdpAddress,
                    lpaString: orderJson.stockHistory.lpaString,
                    accessPointName: orderJson.stockHistory.accessPointName,
                    phoneNumber: orderJson.stockHistory.phoneNumber
                })
            };

            // Generate QR code if we have lpaString
            let qrCode = null;
            const lpaString = esimDetails.lpaString || esimDetails.qrCodeUrl;
            if (lpaString) {
                qrCode = await generateQRCode(lpaString);
            }

            return {
                id: orderJson.id,
                plan: orderJson.plan,
                orderTotal: orderJson.orderTotal,
                startDate: orderJson.startDate,
                validTime: orderJson.validTime,
                status: orderJson.status,
                createdAt: orderJson.createdAt,
                quantity: orderJson.quantity,
                // Include usage data fields
                dataUsage: orderJson.dataUsage,
                dataAllowance: orderJson.dataAllowance,
                usageStatus: orderJson.usageStatus,
                expiryDate: orderJson.expiryDate,
                usageMessage: orderJson.usageMessage,
                lastUsageCheck: orderJson.lastUsageCheck,
                usageData: orderJson.usageData,
                providerResponse: orderJson.providerResponse,
                providerMetadata: orderJson.providerMetadata,
                esimDetails,
                qrCode
            };
        }));

        return res.json(formattedOrders);
    } catch (error) {
        console.error('Error fetching orders:', error);
        return res.status(500).json({ 
            message: 'Failed to fetch orders',
            error: error.message 
        });
    }
};

// Get a specific order by ID
exports.getOrderById = async (req, res) => {
    try {
        const id = req.params.id;
        
        if (!id || typeof id !== 'string' || !id.startsWith('VLZ')) {
            return res.status(400).json({ 
                message: 'Invalid order ID format',
                details: 'Order ID must be in the format VLZxxxx'
            });
        }

        const order = await models.Order.findOne({
            where: { id },
            include: [
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    include: [{
                        model: models.Provider,
                        as: 'provider',
                        attributes: ['id', 'name', 'type', 'country']
                    }]
                },
                {
                    model: models.EsimStock,
                    as: 'stock',
                    attributes: [
                        'id', 'iccid', 'smdpAddress', 'lpaString', 
                        'accessPointName', 'activationCode', 'phoneNumber', 
                        'status', 'providerMetadata'
                    ]
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: [
                        'id', 'iccid', 'smdpAddress', 'lpaString', 
                        'accessPointName', 'activationCode', 'phoneNumber',
                        'status', 'orderDate', 'quantity', 'reason'
                    ]
                }
            ]
        });

        if (!order) {
            return res.status(404).json({
                message: 'Order not found'
            });
        }

        // Extract eSIM details from either Mobimatter response or local stock
        let esimDetails = {};
        
        if (order.plan?.provider?.name === 'Mobimatter' && order.providerResponse?.orderLineItem?.lineItemDetails) {
            const lineItems = order.providerResponse.orderLineItem.lineItemDetails;
            esimDetails = {
                iccid: lineItems.find(item => item.name === 'ICCID')?.value,
                smdpAddress: lineItems.find(item => item.name === 'SMDP_ADDRESS')?.value,
                lpaString: lineItems.find(item => item.name === 'LOCAL_PROFILE_ASSISTANT')?.value,
                accessPointName: lineItems.find(item => item.name === 'ACCESS_POINT_NAME')?.value,
                activationCode: lineItems.find(item => item.name === 'ACTIVATION_CODE')?.value,
                phoneNumber: lineItems.find(item => item.name === 'PHONE_NUMBER')?.value,
                qrCodeUrl: lineItems.find(item => item.name === 'QR_CODE')?.value,
                confCode: lineItems.find(item => item.name === 'CONF_CODE')?.value,
                walletAuthTransactionId: lineItems.find(item => item.name === 'WALLET_AUTH_TRANSACTION_ID')?.value
            };
        } else {
            // For local orders, use stock or stockHistory data
            esimDetails = {
                iccid: order.stock?.iccid || order.stockHistory?.iccid,
                smdpAddress: order.stock?.smdpAddress || order.stockHistory?.smdpAddress,
                lpaString: order.stock?.lpaString || order.stockHistory?.lpaString,
                accessPointName: order.stock?.accessPointName || order.stockHistory?.accessPointName,
                activationCode: order.stock?.activationCode || order.stockHistory?.activationCode,
                phoneNumber: order.stock?.phoneNumber || order.stockHistory?.phoneNumber,
                qrCodeUrl: null
            };
        }

        // Generate QR code if we have lpaString but no qrCodeUrl
        if (!esimDetails.qrCodeUrl && esimDetails.lpaString) {
            try {
                esimDetails.qrCodeUrl = await generateQRCode(esimDetails.lpaString);
            } catch (error) {
                console.error('Error generating QR code:', error);
            }
        }

        // Format the response
        const formattedOrder = {
            id: order.id,
            externalOrderId: order.externalOrderId,
            status: order.status,
            orderTotal: order.orderTotal,
            quantity: order.quantity,
            startDate: order.startDate,
            validTime: order.validTime,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt,
            // Include usage data fields
            dataUsage: order.dataUsage,
            dataAllowance: order.dataAllowance,
            usageStatus: order.usageStatus,
            expiryDate: order.expiryDate,
            usageMessage: order.usageMessage,
            lastUsageCheck: order.lastUsageCheck,
            usageData: order.usageData,
            plan: order.plan ? {
                id: order.plan.id,
                name: order.plan.name,
                activationPolicy: order.plan.activationPolicy,
                provider: order.plan.provider ? {
                    id: order.plan.provider.id,
                    name: order.plan.provider.name,
                    type: order.plan.provider.type
            } : null
            } : null,
            stock: {
                ...esimDetails,
                status: order.stock?.status || order.stockHistory?.status
            },
            providerDetails: {
                orderStatus: order.providerOrderStatus,
                errorMessage: order.providerErrorMessage,
                lastCheck: order.lastProviderCheck,
                response: order.providerResponse,
                metadata: order.providerMetadata
            }
        };

        return res.json(formattedOrder);

    } catch (error) {
        console.error('Error fetching order:', error);
        return res.status(500).json({ 
            message: 'Failed to fetch order',
            error: error.message 
        });
    }
};

// Get all orders (admin only)
exports.getAllOrders = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const search = req.query.search?.toLowerCase() || '';

        // Build search conditions
        const searchConditions = search ? {
            [Op.or]: [
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('user.firstName')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('user.lastName')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('user.email')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('plan.name')),
                    { [Op.like]: `%${search}%` }
                ),
                sequelize.where(
                    sequelize.fn('LOWER', sequelize.col('Order.status')),
                    { [Op.like]: `%${search}%` }
                ),
                {
                    id: { [Op.like]: `%${search}%` }
                }
            ]
        } : {};

        // Get orders with count
        const { count, rows: orders } = await models.Order.findAndCountAll({
            where: searchConditions,
            include: [
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['id', 'firstName', 'lastName', 'email']
                },
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['name']
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: ['iccid']
                }
            ],
            order: [['createdAt', 'DESC']],
            limit,
            offset,
            distinct: true
        });

        // Format orders for response
        const formattedOrders = orders.map(order => {
            const json = order.toJSON();
            return {
                id: json.id,
                customer: `${json.user?.firstName} ${json.user?.lastName}`.trim() || 'N/A',
                customerEmail: json.user?.email || 'N/A',
                plan: json.plan,
                iccid: json.stockHistory?.iccid || json.providerMetadata?.iccid || 'N/A',
                quantity: 1,
                orderTotal: json.orderTotal,
                status: json.status,
                createdAt: json.createdAt
            };
        });

        res.json({
            orders: formattedOrders,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalCount: count
        });
    } catch (error) {
        console.error('Error fetching orders:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Get orders by user ID (admin only)
exports.getOrdersByUserId = async (req, res) => {
    try {
        const { userId } = req.params;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;

        const { count, rows: orders } = await models.Order.findAndCountAll({
            where: { userId },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                attributes: ['id', 'name', 'description', 'planData', 'planType', 'planDataUnit', 'validityDays', 'category', 'planCategory', 'customPlanData', 'activationPolicy']
            }, {
                model: models.User,
                as: 'user',
                attributes: ['id', 'firstName', 'lastName', 'email']
            }],
            order: [['createdAt', 'DESC']],
            limit,
            offset
        });

        // Format the response
        const formattedOrders = await Promise.all(orders.map(async (order) => {
            // Get stock details from history
            const stockHistory = await models.EsimPlanStockHistory.findOne({
                where: { orderId: order.id }
            });

            return {
                id: order.id,
                customer: `${order.user.firstName} ${order.user.lastName}`,
                customerEmail: order.user.email,
                plan: {
                    id: order.plan.id,
                    name: order.plan.name,
                    description: order.plan.description,
                    planData: order.plan.planData,
                    planType: order.plan.planType,
                    planDataUnit: order.plan.planDataUnit,
                    validityDays: order.plan.validityDays,
                    category: order.plan.category,
                    customPlanData: order.plan.customPlanData,
                    planCategory: order.plan.planCategory,
                    activationPolicy: order.plan.activationPolicy
                },
                orderTotal: order.orderTotal,
                startDate: order.startDate,
                status: order.status,
                createdAt: order.createdAt,
                quantity: order.quantity,
                externalOrderId: order.externalOrderId,
                // Stock details with fallback to providerMetadata for API orders
                iccid: stockHistory?.iccid || order.providerMetadata?.iccid || null,
                smdpAddress: stockHistory?.smdpAddress || order.providerMetadata?.smdpAddress || null,
                lpaString: stockHistory?.lpaString || order.providerMetadata?.lpaString || null,
                accessPointName: stockHistory?.accessPointName || order.providerMetadata?.accessPointName || null,
                activationCode: stockHistory?.activationCode || order.providerMetadata?.activationCode || null,
                phoneNumber: stockHistory?.phoneNumber || order.providerMetadata?.phoneNumber || null
            };
        }));

        return res.json({
            orders: formattedOrders,
            totalPages: Math.ceil(count / limit),
            currentPage: page,
            totalItems: count
        });
    } catch (error) {
        console.error('Error fetching user orders:', error);
        return res.status(500).json({ message: 'Failed to fetch orders' });
    }
};

// Get order by ID (Admin Only)
exports.getAllOrderById = async (req, res) => {
    try {
        const order = await models.Order.findByPk(req.params.id, {
            include: [
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['id', 'name', 'description', 'planData', 'planType', 'planDataUnit', 'validityDays', 'category', 'planCategory', 'customPlanData', 'activationPolicy'],
                    include: [{
                        model: models.Provider,
                        as: 'provider',
                        attributes: ['id', 'name', 'type', 'country']
                    }]
                },
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['id', 'firstName', 'lastName', 'email']
                }
            ],
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Get stock details from history
        const stockHistory = await models.EsimPlanStockHistory.findOne({
            where: { orderId: order.id }
        });

        // Generate QR code if we have lpaString (from stockHistory or providerMetadata)
        let qrCode = null;
        const lpaString = stockHistory?.lpaString || order.providerMetadata?.lpaString;
        
        if (lpaString) {
            try {
                qrCode = await generateQRCode(lpaString);
                // Remove the 'data:image/png;base64,' prefix
                qrCode = qrCode.split(',')[1];
            } catch (error) {
                console.error('Error generating QR code:', error);
            }
        } else if (order.providerMetadata?.qrCodeUrl) {
            // Use existing QR code if available
            qrCode = order.providerMetadata.qrCodeUrl;
        }

        // Format the response
        const formattedOrder = {
            id: order.id,
            customer: `${order.user.firstName} ${order.user.lastName}`,
            customerEmail: order.user.email,
            plan: {
                id: order.plan.id,
                name: order.plan.name,
                description: order.plan.description,
                planData: order.plan.planData,
                planType: order.plan.planType,
                planDataUnit: order.plan.planDataUnit,
                validityDays: order.plan.validityDays,
                category: order.plan.category,
                customPlanData: order.plan.customPlanData,
                planCategory: order.plan.planCategory,
                activationPolicy: order.plan.activationPolicy,
                provider: order.plan.provider ? {
                    id: order.plan.provider.id,
                    name: order.plan.provider.name,
                    type: order.plan.provider.type,
                    country: order.plan.provider.country
                } : null
            },
            orderTotal: order.orderTotal,
            startDate: order.startDate,
            validTime: order.validTime,
            status: order.status,
            createdAt: order.createdAt,
            quantity: order.quantity,
            externalOrderId: order.externalOrderId,
            // Include usage data fields
            dataUsage: order.dataUsage,
            dataAllowance: order.dataAllowance,
            usageStatus: order.usageStatus,
            expiryDate: order.expiryDate,
            usageMessage: order.usageMessage,
            lastUsageCheck: order.lastUsageCheck,
            usageData: order.usageData,
            // Stock details with fallback to providerMetadata for API orders
            iccid: stockHistory?.iccid || order.providerMetadata?.iccid || null,
            smdpAddress: stockHistory?.smdpAddress || order.providerMetadata?.smdpAddress || null,
            lpaString: stockHistory?.lpaString || order.providerMetadata?.lpaString || null,
            accessPointName: stockHistory?.accessPointName || order.providerMetadata?.accessPointName || null,
            activationCode: stockHistory?.activationCode || order.providerMetadata?.activationCode || null,
            phoneNumber: stockHistory?.phoneNumber || order.providerMetadata?.phoneNumber || null,
            qrCode: qrCode || (order.providerMetadata?.qrCodeUrl ? order.providerMetadata.qrCodeUrl : null)
        };

        return res.json(formattedOrder);
    } catch (error) {
        console.error('Error fetching order:', error);
        return res.status(500).json({ message: 'Failed to fetch order details' });
    }
};

// Export all orders (admin only)
exports.exportOrders = async (req, res) => {
    try {
        // Get all orders with related data
        const orders = await models.Order.findAll({
            include: [
                {
                    model: models.User,
                    as: 'user',
                    attributes: ['id', 'firstName', 'lastName', 'email']
                },
                {
                    model: models.EsimPlan,
                    as: 'plan',
                    attributes: ['name']
                },
                {
                    model: models.EsimPlanStockHistory,
                    as: 'stockHistory',
                    attributes: ['iccid']
                }
            ],
            order: [['createdAt', 'DESC']]
        });

        // Format orders for export
        const formattedOrders = orders.map(order => {
            const json = order.toJSON();
            return {
                id: json.id,
                customer: `${json.user?.firstName} ${json.user?.lastName}` || 'N/A',
                customerEmail: json.user?.email || 'N/A',
                plan: json.plan,
                iccid: json.stockHistory?.iccid || 'N/A',
                quantity: 1, 
                orderTotal: json.orderTotal,
                status: json.status,
                createdAt: json.createdAt
            };
        });

        res.json({ orders: formattedOrders });
    } catch (error) {
        console.error('Error exporting orders:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};

// Get usage data for an order
exports.getOrderUsage = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = req.user.id;

        console.log('Fetching usage data for order:', id);

        // Find the order with plan and provider details
        const order = await models.Order.findOne({
            where: { id },
            include: [{
                model: models.EsimPlan,
                as: 'plan',
                include: [{
                    model: models.Provider,
                    as: 'provider'
                }]
            }]
        });

        console.log('Found order:', {
            id: order?.id,
            provider: order?.plan?.provider?.name,
            externalOrderId: order?.externalOrderId,
            providerResponse: order?.providerResponse
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        // Check if user has access to this order
        if (order.userId !== userId && req.user.role !== 'admin') {
            return res.status(403).json({ message: 'Access denied' });
        }

        // Check if the order is from Mobimatter
        if (order.plan?.provider?.name !== 'Mobimatter') {
            return res.status(400).json({ message: 'Usage data is not available for this order' });
        }

        // Get the external order ID
        const externalOrderId = order.externalOrderId || order.providerResponse?.id;
        if (!externalOrderId) {
            console.error('Order details:', {
                id: order.id,
                externalOrderId: order.externalOrderId,
                providerResponse: order.providerResponse,
                providerMetadata: order.providerMetadata
            });
            return res.status(400).json({ message: 'External order ID not found' });
        }

        console.log('Fetching usage data from Mobimatter for external order:', externalOrderId);

        // Get usage data from Mobimatter
        const usageData = await mobimatterService.getUsageInfo(externalOrderId);

        console.log('Mobimatter usage data response:', usageData);

        // Store the usage data in the database
        try {
            await order.update({
                usageData: usageData,
                dataUsage: usageData.dataUsage || null,
                dataAllowance: usageData.dataAllowance || null,
                usageStatus: usageData.status || 'Unknown',
                expiryDate: usageData.expiryDate || null,
                usageMessage: usageData.message || null,
                lastUsageCheck: new Date()
            });
            console.log('Usage data stored in database for order:', id);
        } catch (updateError) {
            console.error('Error storing usage data in database:', updateError);
            // Continue processing - don't fail the request if storage fails
        }

        res.json(usageData);
    } catch (error) {
        console.error('Error getting order usage:', error);
        console.error('Error stack:', error.stack);
        res.status(500).json({ message: 'Failed to get usage data', error: error.message });
    }
};

/**
 * Handle BillionConnect webhook notifications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleBillionConnectWebhook = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { tradeType, tradeTime, tradeData } = req.body;

        console.log('Received BillionConnect webhook:', {
            tradeType,
            tradeTime,
            orderId: tradeData?.orderId,
            channelOrderId: tradeData?.channelOrderId,
            body: JSON.stringify(req.body)
        });

        // Only handle N009 notifications (ESIM QR code info)
        if (tradeType !== 'N009') {
            console.log('Ignoring non-N009 webhook:', tradeType);
            return res.json({
                tradeCode: '1000',
                tradeMsg: 'Notification received but not processed'
            });
        }

        const { orderId, channelOrderId, subOrderList } = tradeData;

        console.log('Looking for order with externalOrderId:', orderId);

        // Find the order in our database
        const order = await models.Order.findOne({
            where: { externalOrderId: orderId },
            include: [
                { model: models.User, as: 'user' },
                { model: models.EsimPlan, as: 'plan' }
            ],
            transaction: t
        });

        if (!order) {
            console.error(`Order not found for BillionConnect orderId: ${orderId}`);
            return res.json({
                tradeCode: '1000',
                tradeMsg: 'Order not found but acknowledged'
            });
        }

        console.log('Processing BillionConnect webhook for order:', {
            orderId: order.id,
            externalOrderId: orderId,
            status: order.status,
            userId: order.userId,
            planId: order.esimPlanId
        });

        // Process each sub-order (usually just one)
        for (const subOrder of subOrderList) {
            const {
                iccid,
                qrCodeContent,
                apn,
                validTime
            } = subOrder;

            // Parse LPA string to extract smdpAddress, activationCode, and lpaString
            const lpaData = parseLpaString(qrCodeContent);

            if (!lpaData) {
                console.error('Failed to parse LPA string from qrCodeContent:', qrCodeContent);
                throw new Error('Invalid LPA format in webhook data');
            }

            const { smdpAddress, activationCode, lpaString } = lpaData;

            console.log('Processing eSIM details:', {
                iccid,
                smdpAddress,
                apn,
                hasQrCode: !!qrCodeContent,
                hasActivationCode: !!activationCode,
                lpaString: lpaString
            });

            // Check if a stock record with this ICCID already exists
            let stock = await models.EsimStock.findOne({
                where: { iccid: iccid },
                transaction: t,
                lock: true
            });

            const stockData = {
                esimPlanId: order.esimPlanId,
                orderId: order.id,
                iccid: iccid,
                smdpAddress: smdpAddress,
                lpaString: lpaString,
                accessPointName: apn,
                activationCode: activationCode,
                qrCodeUrl: qrCodeContent,
                status: 'assigned',
                externalStockId: orderId,
                externalIccid: iccid,
                providerStatus: 'ACTIVE',
                providerMetadata: subOrder,
                walletAuthTransactionId: order.walletAuthTransactionId
            };

            if (stock) {
                console.log('Updating existing stock record:', stock.id);
                await stock.update(stockData, { transaction: t });
            } else {
                console.log('Creating new stock record');
                stock = await models.EsimStock.create({
                    id: uuidv4(),
                    ...stockData
                }, { transaction: t });
            }

            console.log('Creating stock history record');
            // Create stock history record
            await models.EsimPlanStockHistory.create({
                id: uuidv4(),
                esimPlanId: order.esimPlanId,
                esimStockId: stock.id,
                iccid: iccid,
                smdpAddress: smdpAddress,
                lpaString: lpaString,
                accessPointName: apn,
                activationCode: activationCode,
                orderId: order.id,
                orderDate: new Date(),
                quantity: 1,
                status: 'assigned',
                reason: 'BillionConnect webhook activation',
                createdBy: order.userId
            }, { transaction: t });

            console.log('Updating order status to completed');
            // Update order with stock reference and completed status
            await order.update({
                status: 'completed',
                esimStockId: stock.id,
                providerOrderStatus: 'ACTIVE',
                validTime: validTime ? new Date(validTime) : null,
                providerMetadata: {
                    iccid: iccid,
                    smdpAddress: smdpAddress,
                    lpaString: lpaString,
                    accessPointName: apn,
                    activationCode: activationCode,
                    qrCodeUrl: qrCodeContent,
                    validTime: validTime
                }
            }, { transaction: t });
            // Send email notifications for completed BillionConnect order
            try {
                console.log('Sending email notifications for BillionConnect order:', order.id);

                // Get user details for email
                const user = await models.User.findByPk(order.userId);
                if (user) {
                    // Generate QR code for email
                    let qrCode = null;
                    if (lpaString) {
                        try {
                            qrCode = await generateQRCode(lpaString);
                        } catch (error) {
                            console.error('Error generating QR code for email:', error);
                        }
                    }

                    // Prepare order details for email
                    const orderDetails = {
                        order: {
                            id: order.id,
                            externalOrderId: orderId
                        },
                        plan: order.plan,
                        orderTotal: order.orderTotal,
                        startDate: order.startDate,
                        validTime: validTime ? new Date(validTime) : null,
                        quantity: order.quantity,
                        walletAuthTransactionId: order.walletAuthTransactionId,
                        qrCode: qrCode,
                        partner: {
                            id: user.id,
                            firstName: user.firstName,
                            lastName: user.lastName,
                            email: user.email
                        },
                        stock: {
                            iccid: iccid,
                            smdpAddress: smdpAddress,
                            lpaString: lpaString,
                            accessPointName: apn,
                            activationCode: activationCode,
                            phoneNumber: null
                        }
                    };

                    // Send partner email
                    await emailService.sendPartnerOrderEmail(user.email, orderDetails);
                    console.log('BillionConnect order email sent to partner:', user.email);

                    // Send admin notifications
                    const admins = await models.User.findAll({
                        where: {
                            role: 'admin',
                            isActive: true
                        },
                        attributes: ['email']
                    });

                    if (admins && admins.length > 0) {
                        const adminEmails = admins.map(admin => admin.email);
                        await emailService.sendAdminOrderEmail(adminEmails, orderDetails);
                        console.log('BillionConnect order admin notifications sent');
                    }
                }
            } catch (emailError) {
                console.error('Error sending BillionConnect order emails:', emailError);
                // Don't fail the webhook response due to email errors
            }
        }

        await t.commit();
        console.log('Webhook processing completed successfully');

        // Send success response
        res.json({
            tradeCode: '1000',
            tradeMsg: 'Success'
        });
    } catch (error) {
        await t.rollback();
        console.error('Error handling BillionConnect webhook:', error);
        res.status(500).json({
            tradeCode: '9999',
            tradeMsg: 'Internal server error'
        });
    }
};

module.exports = exports;
