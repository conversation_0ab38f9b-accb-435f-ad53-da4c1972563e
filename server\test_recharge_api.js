// Test script for the recharge API endpoint
require('dotenv').config();

const { EsimPlan, Order, EsimStock, Provider } = require('./src/models');

async function testRechargeAPI() {
    console.log('🧪 Testing Recharge API Functionality...\n');

    try {
        // First, let's check if we have any BillionConnect orders
        console.log('📋 Checking for BillionConnect orders...');
        
        const billionConnectOrders = await Order.findAll({
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [{
                    model: Provider,
                    as: 'provider',
                    where: {
                        name: 'Billionconnect'
                    }
                }]
            }, {
                model: EsimStock,
                as: 'stock'
            }],
            limit: 5
        });

        console.log(`Found ${billionConnectOrders.length} BillionConnect orders`);

        if (billionConnectOrders.length === 0) {
            console.log('❌ No BillionConnect orders found to test with');
            return;
        }

        // Test with the first order
        const testOrder = billionConnectOrders[0];
        console.log(`\n🔍 Testing with Order #${testOrder.id}`);
        console.log(`   Plan: ${testOrder.plan.name}`);
        console.log(`   Status: ${testOrder.status}`);
        console.log(`   ICCID: ${testOrder.stock?.iccid || 'N/A'}`);

        // Check rechargeable plans
        console.log('\n📱 Checking rechargeable plans...');
        
        const rechargeablePlans = await EsimPlan.findAll({
            where: {
                top_up: true,
                isActive: true
            },
            include: [{
                model: Provider,
                as: 'provider',
                where: {
                    name: 'Billionconnect'
                }
            }]
        });

        console.log(`Found ${rechargeablePlans.length} rechargeable plans:`);
        rechargeablePlans.forEach(plan => {
            console.log(`   - ${plan.name} (SKU: ${plan.externalSkuId}) - ${plan.currency} ${plan.price}`);
        });

        // Simulate the API response
        const mockApiResponse = {
            available: rechargeablePlans.length > 0 && testOrder.status === 'completed' && testOrder.stock?.iccid,
            reason: rechargeablePlans.length === 0 ? 'No rechargeable plans available' : 
                   testOrder.status !== 'completed' ? 'Order not completed' :
                   !testOrder.stock?.iccid ? 'No ICCID available' : null,
            rechargeablePlans: rechargeablePlans.map(plan => ({
                id: plan.id,
                name: plan.name,
                externalSkuId: plan.externalSkuId,
                price: plan.price,
                currency: plan.currency,
                data: plan.data,
                validity: plan.validity,
                countries: plan.countries,
                description: plan.description
            })),
            orderInfo: {
                id: testOrder.id,
                iccid: testOrder.stock?.iccid,
                planName: testOrder.plan.name
            }
        };

        console.log('\n📊 API Response Preview:');
        console.log(JSON.stringify(mockApiResponse, null, 2));

        console.log('\n✅ Recharge API test completed successfully!');
        
        if (mockApiResponse.available) {
            console.log('🎉 Recharge functionality is available for this order!');
        } else {
            console.log(`⚠️  Recharge not available: ${mockApiResponse.reason}`);
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Test the recharge button visibility logic
async function testRechargeButtonLogic() {
    console.log('\n🔘 Testing Recharge Button Visibility Logic...\n');

    try {
        const orders = await Order.findAll({
            include: [{
                model: EsimPlan,
                as: 'plan',
                include: [{
                    model: Provider,
                    as: 'provider'
                }]
            }, {
                model: EsimStock,
                as: 'stock'
            }],
            limit: 10
        });

        console.log('Order Analysis:');
        console.log('================');

        orders.forEach(order => {
            const isBillionConnect = order.plan?.provider?.name === 'Billionconnect';
            const isCompleted = order.status === 'completed';
            const hasIccid = !!order.stock?.iccid;
            const shouldShowButton = isBillionConnect && isCompleted && hasIccid;

            console.log(`Order #${order.id}:`);
            console.log(`  Provider: ${order.plan?.provider?.name || 'N/A'} ${isBillionConnect ? '✅' : '❌'}`);
            console.log(`  Status: ${order.status} ${isCompleted ? '✅' : '❌'}`);
            console.log(`  ICCID: ${hasIccid ? 'Present ✅' : 'Missing ❌'}`);
            console.log(`  Show Recharge Button: ${shouldShowButton ? '✅ YES' : '❌ NO'}`);
            console.log('');
        });

    } catch (error) {
        console.error('❌ Button logic test failed:', error.message);
    }
}

async function runTests() {
    await testRechargeAPI();
    await testRechargeButtonLogic();
    
    console.log('\n🎉 All tests completed!');
    process.exit(0);
}

runTests();
