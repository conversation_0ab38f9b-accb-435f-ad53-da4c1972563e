// Test script for BillionConnect recharge order functionality
require('dotenv').config();

const billionconnectService = require('./src/services/billionconnect.service');

async function testRechargeOrder() {
    console.log('🧪 Testing BillionConnect Recharge Order Functionality...\n');

    try {
        // Test data for recharge order
        const testOrderData = {
            channelOrderId: `TEST_ORDER_${Date.now()}`,
            totalAmount: "50.00",
            orderCreateTime: new Date().toISOString().replace('T', ' ').split('.')[0],
            comment: "Test recharge order from API",
            subOrderList: [
                {
                    channelSubOrderId: `SUB_ORDER_${Date.now()}_1`,
                    iccid: [
                        "89860012017300000001",
                        "89860012017300000002"
                    ],
                    skuId: "1745221471586109", // Korea Unlimited plan
                    copies: "2",
                    price: "25.00"
                }
            ]
        };

        console.log('📋 Test Order Data:');
        console.log(JSON.stringify(testOrderData, null, 2));
        console.log('\n');

        // Create recharge order
        console.log('🚀 Creating recharge order...');
        const result = await billionconnectService.createRechargeOrder(testOrderData);

        console.log('✅ Recharge order created successfully!');
        console.log('📄 Response:');
        console.log(JSON.stringify(result, null, 2));

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Test validation errors
async function testValidationErrors() {
    console.log('\n🧪 Testing Validation Errors...\n');

    const testCases = [
        {
            name: 'Missing channelOrderId',
            data: {
                subOrderList: [
                    {
                        channelSubOrderId: "SUB_001",
                        iccid: ["89860012017300000001"],
                        skuId: "1745221471583108",
                        copies: "1"
                    }
                ]
            }
        },
        {
            name: 'Empty subOrderList',
            data: {
                channelOrderId: "TEST_001",
                subOrderList: []
            }
        },
        {
            name: 'Missing ICCID in subOrder',
            data: {
                channelOrderId: "TEST_002",
                subOrderList: [
                    {
                        channelSubOrderId: "SUB_001",
                        skuId: "1745221471583108",
                        copies: "1"
                    }
                ]
            }
        },
        {
            name: 'Too many ICCIDs (>500)',
            data: {
                channelOrderId: "TEST_003",
                subOrderList: [
                    {
                        channelSubOrderId: "SUB_001",
                        iccid: Array(501).fill().map((_, i) => `8986001201730000${String(i).padStart(4, '0')}`),
                        skuId: "1745221471583108",
                        copies: "1"
                    }
                ]
            }
        }
    ];

    for (const testCase of testCases) {
        try {
            console.log(`🔍 Testing: ${testCase.name}`);
            await billionconnectService.createRechargeOrder(testCase.data);
            console.log('❌ Expected validation error but got success');
        } catch (error) {
            console.log(`✅ Correctly caught validation error: ${error.message}`);
        }
        console.log('');
    }
}

async function runTests() {
    await testRechargeOrder();
    await testValidationErrors();
    
    console.log('\n🎉 All tests completed!');
}

runTests();
