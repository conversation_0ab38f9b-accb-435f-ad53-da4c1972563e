# Provider Visibility Implementation

## Overview
This implementation ensures that when a provider's plans are hidden, any newly added plans for that provider will also be automatically hidden instead of being displayed as visible.

## Problem Statement
Previously, when a provider's plans were hidden using the "Hide Plans" functionality, any new plans added to that provider (either manually or through sync operations) would default to "visible" status, making them appear in the system despite the provider being intentionally hidden.

## Solution
Added a helper function `isProviderPlansHidden()` that checks if a provider's existing plans are currently hidden, and modified all plan creation functions to respect this status.

## Implementation Details

### 1. Helper Function
```javascript
const isProviderPlansHidden = async (providerId) => {
    if (!providerId) return false;
    
    try {
        // Check if there are any visible plans for this provider
        const visiblePlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                status: 'visible',
                isActive: true
            }
        });
        
        // Check if there are any plans at all for this provider
        const totalPlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                isActive: true
            }
        });
        
        // If there are plans but none are visible, the provider is hidden
        return totalPlanCount > 0 && visiblePlanCount === 0;
    } catch (error) {
        console.error('Error checking provider plan visibility:', error);
        return false; // Default to visible if there's an error
    }
};
```

### 2. Modified Functions

#### createEsimPlan
- Added provider visibility check before creating plan
- Sets plan status to 'hidden' if provider plans are hidden

#### createBulkEsimPlans
- Added provider visibility check for each plan in bulk creation
- Sets plan status to 'hidden' if provider plans are hidden

#### syncExternalPlans
- Added provider visibility check when creating new plans during sync
- Ensures synced plans respect provider visibility

#### syncBillionConnectPlans
- Added provider visibility check for BillionConnect sync
- New BillionConnect plans inherit provider visibility status

#### syncMobimatterPlans
- Added provider visibility check for Mobimatter sync
- New Mobimatter plans inherit provider visibility status

## Logic Flow

1. **Check Provider Status**: Before creating any new plan, the system checks if the provider has existing plans and if they are all hidden
2. **Determine Plan Status**: 
   - If provider has no existing plans → new plan is visible (default)
   - If provider has visible plans → new plan is visible
   - If provider has plans but all are hidden → new plan is hidden
3. **Create Plan**: Plan is created with the determined status

## Benefits

1. **Consistent Behavior**: Provider visibility is maintained across all plan creation methods
2. **Admin Intent Preservation**: When an admin hides a provider's plans, new plans won't accidentally become visible
3. **Sync Operation Safety**: External sync operations respect manual visibility settings
4. **No Breaking Changes**: Existing functionality remains unchanged, only adds the visibility inheritance

## Testing

Added comprehensive tests in `esimPlanController.test.js`:
- Test for creating visible plans when provider has no hidden plans
- Test for creating hidden plans when provider plans are hidden
- Test for bulk plan creation with hidden provider
- Test for sync operations respecting provider visibility

## Files Modified

1. `server/src/controllers/esimPlanController.js`
   - Added `isProviderPlansHidden()` helper function
   - Modified `createEsimPlan()` function
   - Modified `createBulkEsimPlans()` function
   - Modified sync functions: `syncExternalPlans()`, `syncBillionConnectPlans()`, `syncMobimatterPlans()`

2. `server/tests/unit/controllers/esimPlanController.test.js`
   - Added Provider model import
   - Added comprehensive test cases for provider visibility

## Usage Example

```javascript
// When creating a new plan
const providerPlansHidden = await isProviderPlansHidden(planData.providerId);
const processedPlanData = {
    ...planData,
    status: providerPlansHidden ? 'hidden' : (planData.status || 'visible'),
    // ... other fields
};
```

This implementation ensures that the system maintains consistent provider visibility across all plan creation scenarios.
