{"name": "server", "version": "1.0.0", "description": "B2B eSIM Platform Backend", "main": "app.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "node app.js", "dev": "nodemon app.js", "setup-db": "node setup-database.js", "update-plan-metadata": "node updatePlanMetadata.js", "optimize-db": "node scripts/optimize-database.js", "db:optimize": "npm run optimize-db"}, "keywords": ["esim", "b2b", "authentication"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.6.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.6.1", "nanoid": "^3.3.11", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "qrcode": "^1.5.4", "sequelize": "^6.33.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "yamljs": "^0.3.0", "zeptomail": "^3.0.4"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "jest": "^29.7.0", "ngrok": "^5.0.0-beta.2", "nodemon": "^3.1.9", "supertest": "^7.0.0"}}