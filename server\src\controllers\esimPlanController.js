const { EsimPlan, Country, User, EsimPlanCountries, Provider, Feature } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const { sendLowStockEmail } = require('../utils/emailService');
const providerFactory = require('../services/provider.factory');
const generateProductId = require('../utils/generateProductId');
const { getCachedPlan, setCachePlan, invalidatePlanCache } = require('../utils/cacheManager');

// Helper function to check stock and notify admins if needed
const checkStockAndNotify = async (planId) => {
    try {
        // Get plan details with current stock count
        const plan = await EsimPlan.findByPk(planId, {
            include: [
                {
                    model: sequelize.models.EsimStock,
                    as: 'stocks',
                    where: { status: 'available' },
                    required: false
                }
            ]
        });

        if (!plan) return;

        const currentStock = plan.stocks?.length || 0;

        // Check if stock is below threshold
        if (currentStock < plan.stockThreshold) {
            // Get all admin users
            const admins = await User.findAll({
                where: { role: 'admin', isActive: true },
                attributes: ['email']
            });

            const adminEmails = admins.map(admin => admin.email);

            // Send notification
            await sendLowStockEmail(adminEmails, plan, currentStock);
        }
    } catch (error) {
        console.error('Error in stock check and notification:', error);
        // Don't throw error as this is a notification service
    }
};

// Helper function to add delay between API calls
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to check if a provider's plans are currently hidden
const isProviderPlansHidden = async (providerId) => {
    if (!providerId) return false;

    try {
        // Check if there are any visible plans for this provider
        const visiblePlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                status: 'visible',
                isActive: true
            }
        });

        // Check if there are any plans at all for this provider
        const totalPlanCount = await EsimPlan.count({
            where: {
                providerId: providerId,
                isActive: true
            }
        });

        // If there are plans but none are visible, the provider is hidden
        return totalPlanCount > 0 && visiblePlanCount === 0;
    } catch (error) {
        console.error('Error checking provider plan visibility:', error);
        return false; // Default to visible if there's an error
    }
};

// Get all eSIM plans (including external provider plans)
exports.getAllPlans = async (req, res) => {
    try {
        // Get all plans from database (both local and external)
        const allPlans = await EsimPlan.findAll({
            where: { isActive: true },
            include: [{
                model: Provider,
                as: 'provider'
            }]
        });

        res.json({
            success: true,
            data: allPlans
        });
    } catch (error) {
        console.error('Error fetching plans:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch plans',
            error: error.message
        });
    }
};

// Get plan by ID (works for both local and external plans)
exports.getPlanById = async (req, res) => {
    try {
        const { id } = req.params;

        // First try to find local plan
        let plan = await EsimPlan.findOne({
            where: { id },
            include: [{
                model: Provider,
                as: 'provider'
            }]
        });

        // If not found locally, check if it's an external plan
        if (!plan) {
            const providers = await Provider.findAll({ where: { type: 'API', status: 'active' } });

            for (const provider of providers) {
                try {
                    const providerService = providerFactory.getProvider(provider.name);
                    const externalPlan = await providerService.getProductDetails(id);

                    if (externalPlan) {
                        plan = {
                            ...providerFactory.standardizeProduct(provider.name, externalPlan),
                            providerId: provider.id,
                            provider: provider
                        };
                        break;
                    }
                } catch (error) {
                    console.error(`Error fetching plan from provider ${provider.name}:`, error);
                    // Continue checking other providers
                }
            }
        }

        if (!plan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        res.json(plan);
    } catch (error) {
        console.error('Error fetching plan:', error);
        res.status(500).json({ message: 'Failed to fetch plan details' });
    }
};

// Search plans (works for both local and external plans)
exports.searchPlans = async (req, res) => {
    try {
        const { search, countryId, region, category, page = 1, limit = 50 } = req.query;

        // Build the base query
        let whereClause = {
                isActive: true,
            isDeleted: false
        };

        // Add category filter if specified
        if (category) {
            whereClause.category = category;
        }

        // Add country filter if specified
        if (countryId) {
            whereClause['$countries.id$'] = countryId;
        }

        // Add region filter if specified
        if (region) {
            whereClause.region = region;
        }

        // Build search conditions
        const searchConditions = [];
        if (search) {
            const searchTerms = search.toLowerCase().split(' ');
            searchConditions.push(
                ...searchTerms.map(term => ({
                    [Op.or]: [
                        { name: { [Op.iLike]: `%${term}%` } },
                        { networkName: { [Op.iLike]: `%${term}%` } },
                        { planType: { [Op.iLike]: `%${term}%` } },
                        { planCategory: { [Op.iLike]: `%${term}%` } },
                        { region: { [Op.iLike]: `%${term}%` } },
                        { '$countries.name$': { [Op.iLike]: `%${term}%` } },
                        { '$features.name$': { [Op.iLike]: `%${term}%` } }
                    ]
                }))
            );
        }

        // Combine where conditions
        if (searchConditions.length > 0) {
            whereClause = {
                [Op.and]: [
                    whereClause,
                    ...searchConditions
                ]
            };
        }

        // Fetch plans with pagination
        const { count, rows: plans } = await EsimPlan.findAndCountAll({
            where: whereClause,
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] },
                    required: false
                },
                {
                    model: Feature,
                    as: 'features',
                    through: { attributes: [] },
                    required: false
                }
            ],
            order: [['createdAt', 'DESC']],
            limit: parseInt(limit),
            offset: (parseInt(page) - 1) * parseInt(limit),
            distinct: true
        });

        // Calculate total pages
        const totalPages = Math.ceil(count / limit);

        res.json({
            plans,
            total: count,
            totalPages,
            currentPage: parseInt(page),
            hasMore: parseInt(page) < totalPages
        });
    } catch (error) {
        console.error('Error searching plans:', error);
        res.status(500).json({ error: 'Failed to search plans' });
    }
};

// Get all eSIM plans
exports.getEsimPlans = async (req, res) => {
    try {
        const {
            search,
            status,
            category,
            provider,
            providerType,
            countryId,
            region,
            sortBy = 'createdAt',
            sortOrder = 'DESC',
            page = 1,
            limit = 10,
            isActive
        } = req.query;

        // Create cache key for this specific request
        const cacheKey = `admin_esim_plans_${JSON.stringify({
            search, status, category, provider, providerType, countryId, region, sortBy, sortOrder, page, limit, isActive
        })}`;

        // Get local plans
        let whereClause = {
            // Only show active plans by default, unless isActive is explicitly set
            isActive: isActive !== undefined ? (isActive === 'true') : true
        };

        // Add default status filter if not specified in the request
        if (!status) {
            whereClause.status = 'visible';
        }

        const include = [
            {
                model: Country,
                as: 'countries',
                through: { attributes: ['isDefault'] }
            },
            {
                model: Provider,
                as: 'provider',
                attributes: ['id', 'name', 'type', 'country']
            }
        ];

        // Search filter
        if (search) {
            whereClause[Op.or] = [
                { name: { [Op.like]: `%${search}%` } },
                { productId: { [Op.like]: `%${search}%` } }
            ];
        }

        // Status filter
        if (status) {
            whereClause.status = status;
        }

        // Category filter
        if (category) {
            whereClause.category = category;
        }

        // Provider filter
        if (provider) {
            whereClause.providerId = provider;
        }

        // Provider type filter
        if (providerType) {
            include[1].where = { type: providerType };
        }

        // Country filter
        if (countryId) {
            include[0].where = { id: countryId };
        }

        // Region filter
        if (region && region !== 'all') {
            whereClause.region = { [Op.like]: `%${region}%` };
        }

        // Get total count first
        const totalCount = await EsimPlan.count({
            where: whereClause,
            include: include,
            distinct: true
        });

        // Calculate pagination
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;
        const totalPages = Math.ceil(totalCount / limitNum);

        // Validate page number
        if (pageNum > totalPages && totalCount > 0) {
            return res.status(400).json({
                message: 'Invalid page number',
                currentPage: pageNum,
                totalPages: totalPages
            });
        }

        // Get paginated plans
        const plans = await EsimPlan.findAll({
            where: whereClause,
            include: include,
            order: [[sortBy, sortOrder]],
            limit: limitNum,
            offset: offset,
            distinct: true
        });

        // Get all countries and regions for filters
        const countries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3'],
            order: [['name', 'ASC']]
        });

        const regions = await EsimPlan.findAll({
            attributes: [
                [sequelize.fn('DISTINCT', sequelize.col('region')), 'region']
            ],
            where: {
                region: { [Op.not]: null }
            },
            raw: true
        });

        const processedRegions = regions
            .map(r => r.region)
            .filter(Boolean)
            .flatMap(region => region.split(',').map(r => r.trim()))
            .filter((value, index, self) => self.indexOf(value) === index)
            .sort();

        // Get stock counts for local plans
        const stockCounts = await sequelize.models.EsimStock.findAll({
            attributes: [
                'esimPlanId',
                [sequelize.fn('COUNT', sequelize.col('id')), 'stockCount']
            ],
            where: {
                esimPlanId: plans.map(plan => plan.id),
                status: 'available'
            },
            group: ['esimPlanId'],
            raw: true
        });

        // Create a map of plan ID to stock count
        const stockCountMap = stockCounts.reduce((acc, stock) => {
            acc[stock.esimPlanId] = parseInt(stock.stockCount);
            return acc;
        }, {});

        // Add stock counts to local plans
        const plansWithStock = plans.map(plan => {
            // Check if plan is a Sequelize instance
            const planData = typeof plan.toJSON === 'function' ? plan.toJSON() : plan;
            
            // For API providers, set stockCount to "Unlimited"
            if (planData.provider?.type === 'API') {
                planData.stockCount = "Unlimited";
            } else {
                // For custom providers, use actual stock count
                planData.stockCount = stockCountMap[planData.id] || 0;
            }
            
            return planData;
        });

        const responseData = {
            plans: plansWithStock,
            page: pageNum,
            totalPages,
            totalItems: totalCount,
            countries,
            regions: processedRegions
        };

        // Cache for 5 minutes
        setCachePlan(cacheKey, responseData, 300);

        res.json(responseData);
    } catch (error) {
        console.error('Error fetching eSIM plans:', error);
        res.status(500).json({
            message: 'Failed to fetch eSIM plans',
            error: error.message
        });
    }
};

// Get a single eSIM plan by ID
exports.getEsimPlan = async (req, res) => {
    try {
        const plan = await EsimPlan.findByPk(req.params.id, {
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                },
                {
                    model: Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ]
        });

        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        res.json(plan);
    } catch (error) {
        console.error('Error fetching eSIM plan:', error);
        res.status(500).json({ message: 'Failed to fetch eSIM plan' });
    }
};

// Create new eSIM plan
exports.createEsimPlan = async (req, res) => {
    try {
        const {
            countries,
            defaultCountryId,
            ...planData
        } = req.body;

        // If defaultCountryId is not provided, use the first country
        const effectiveDefaultCountryId = defaultCountryId || countries[0];

        // Check if provider's plans are currently hidden
        const providerPlansHidden = await isProviderPlansHidden(planData.providerId);

        // Handle voiceMin for Data Only plans
        const processedPlanData = {
            ...planData,
            voiceMin: planData.planCategory === 'Data Only' ? null : planData.voiceMin,
            voiceMinUnit: planData.planCategory === 'Data Only' ? null : planData.voiceMinUnit,
            startDateEnabled: planData.startDateEnabled || false,
            is_voice: planData.planCategory === 'Voice and Data' ? 'Available' : 'Not Available',
            is_sms: planData.is_sms || 'Not Available',
            sms: planData.is_sms === 'Available' ? planData.sms : null,
            // Set activation policy to 'Activation upon travel date' when start date is enabled
            activationPolicy: planData.startDateEnabled ? 'Activation upon travel date' : (planData.activationPolicy || 'Activation upon purchase'),
            // Set status based on provider's current visibility
            status: providerPlansHidden ? 'hidden' : (planData.status || 'visible'),
            id: uuidv4()
        };

        // Create the plan
        const plan = await EsimPlan.create(processedPlanData);

        // Associate countries with the plan
        await Promise.all(countries.map(countryId =>
            plan.addCountry(countryId, {
                through: {
                    id: uuidv4(),
                    isDefault: countryId === effectiveDefaultCountryId
                }
            })
        ));

        // Fetch the plan with its countries and provider
        const createdPlan = await EsimPlan.findByPk(plan.id, {
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                },
                {
                    model: Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ]
        });

        res.status(201).json(createdPlan);
    } catch (error) {
        console.error('Error creating eSIM plan:', error);
        res.status(400).json({
            message: 'Failed to create eSIM plan',
            error: error.message
        });
    }
};

// Create multiple eSIM plans from bulk upload
exports.createBulkEsimPlans = async (req, res) => {
    try {
        const plansData = req.body;

        if (!Array.isArray(plansData) || plansData.length === 0) {
            return res.status(400).json({
                message: 'Invalid data format. Expected an array of plan objects.'
            });
        }

        const createdPlans = [];
        const errors = [];

        // Create a base timestamp for maintaining order
        const baseTimestamp = new Date();

        // Process each plan individually to provide detailed error reporting
        for (let i = 0; i < plansData.length; i++) {
            const planData = plansData[i];
            const rowIndex = planData.rowIndex || i + 1;

            try {
                // Validate required fields
                const requiredFields = ['name', 'networkName', 'buyingPrice', 'validityDays', 'countries'];
                const missingFields = requiredFields.filter(field => {
                    if (field === 'countries') {
                        return !planData.countries || !Array.isArray(planData.countries) || planData.countries.length === 0;
                    }
                    return !planData[field];
                });

                if (missingFields.length > 0) {
                    errors.push({
                        row: rowIndex,
                        error: `Missing required fields: ${missingFields.join(', ')}`
                    });
                    continue;
                }

                // Validate enum values
                const validPlanTypes = ['Fixed', 'Unlimited', 'Custom'];
                const validCategories = ['esim_realtime', 'esim_addon', 'esim_replacement'];
                const validPlanCategories = ['Voice and Data', 'Data Only'];
                const validVoiceOptions = ['Available', 'Not Available'];
                const validSmsOptions = ['Available', 'Not Available'];
                const validTopUpOptions = ['Available', 'Not Available'];
                const validHotspotOptions = ['Available', 'Not Available'];
                const validActivationPolicies = ['Activation upon purchase', 'Activation upon first usage', 'Activation upon travel date'];
                const validSpeedOptions = ['Restricted', 'Unrestricted'];
                const validDataUnits = ['MB', 'GB', 'TB'];
                const validVoiceUnits = ['Min', 'Hr', 'Sec'];

                if (planData.planType && !validPlanTypes.includes(planData.planType)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid plan type: ${planData.planType}. Must be one of: ${validPlanTypes.join(', ')}`
                    });
                    continue;
                }

                if (planData.category && !validCategories.includes(planData.category)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid category: ${planData.category}. Must be one of: ${validCategories.join(', ')}`
                    });
                    continue;
                }

                if (planData.planCategory && !validPlanCategories.includes(planData.planCategory)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid plan category: ${planData.planCategory}. Must be one of: ${validPlanCategories.join(', ')}`
                    });
                    continue;
                }

                // Validate data unit if plan data is provided
                if (planData.planData && planData.planDataUnit && !validDataUnits.includes(planData.planDataUnit)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid plan data unit: ${planData.planDataUnit}. Must be one of: ${validDataUnits.join(', ')}`
                    });
                    continue;
                }

                // Validate voice unit if voice minutes are provided
                if (planData.voiceMin && planData.voiceMinUnit && !validVoiceUnits.includes(planData.voiceMinUnit)) {
                    errors.push({
                        row: rowIndex,
                        error: `Invalid voice unit: ${planData.voiceMinUnit}. Must be one of: ${validVoiceUnits.join(', ')}`
                    });
                    continue;
                }

                // Handle both country IDs and country names
                let countryIds = [];

                if (planData.countries && planData.countries.length > 0) {
                    // Check if we have country IDs (2-letter codes) or country names
                    const firstCountry = planData.countries[0];
                    const isCountryId = typeof firstCountry === 'string' && firstCountry.length <= 3;

                    if (isCountryId) {
                        // Validate country IDs
                        const countries = await Country.findAll({
                            where: {
                                id: {
                                    [Op.in]: planData.countries
                                }
                            }
                        });

                        if (countries.length !== planData.countries.length) {
                            const foundCountryIds = countries.map(c => c.id);
                            const invalidCountries = planData.countries.filter(id => !foundCountryIds.includes(id));
                            errors.push({
                                row: rowIndex,
                                error: `Invalid country codes: ${invalidCountries.join(', ')}`
                            });
                            continue;
                        }
                        countryIds = planData.countries;
                    } else {
                        // Convert country names to IDs
                        const countries = await Country.findAll();
                        const countryMap = new Map();
                        countries.forEach(country => {
                            countryMap.set(country.name.toLowerCase(), country.id);
                        });

                        const invalidCountryNames = [];
                        countryIds = [];

                        for (const countryName of planData.countries) {
                            const countryId = countryMap.get(countryName.toLowerCase());
                            if (countryId) {
                                countryIds.push(countryId);
                            } else {
                                invalidCountryNames.push(countryName);
                            }
                        }

                        if (invalidCountryNames.length > 0) {
                            errors.push({
                                row: rowIndex,
                                error: `Invalid country names: ${invalidCountryNames.join(', ')}`
                            });
                            continue;
                        }
                    }
                } else {
                    errors.push({
                        row: rowIndex,
                        error: 'No countries specified'
                    });
                    continue;
                }

                // Check if provider's plans are currently hidden
                const providerPlansHidden = await isProviderPlansHidden(planData.providerId);

                // Prepare plan data for creation with incremented timestamp to maintain order
                const planTimestamp = new Date(baseTimestamp.getTime() + (i * 1000)); // Add 1 second per plan
                const processedPlanData = {
                    name: planData.name,
                    description: planData.description || null,
                    providerId: planData.providerId || null,
                    networkName: planData.networkName,
                    networkType: planData.networkType || '4G/LTE',
                    region: planData.region || null,
                    buyingPrice: planData.buyingPrice,
                    sellingPrice: planData.sellingPrice || null,
                    validityDays: planData.validityDays,
                    planType: planData.planType || 'Fixed',
                    planData: planData.planData || null,
                    planDataUnit: planData.planDataUnit || null,
                    customPlanData: planData.customPlanData || null,
                    category: planData.category || 'esim_realtime',
                    planCategory: planData.planCategory || 'Data Only',
                    is_voice: planData.is_voice || 'Not Available',
                    voiceMin: planData.voiceMin || null,
                    voiceMinUnit: planData.voiceMinUnit || null,
                    is_sms: planData.is_sms || 'Not Available',
                    sms: planData.sms || null,
                    top_up: planData.top_up || 'Not Available',
                    hotspot: planData.hotspot || 'Available',
                    activationPolicy: planData.activationPolicy || 'Activation upon purchase',
                    speed: planData.speed || 'Unrestricted',
                    stockThreshold: planData.stockThreshold || 10,
                    instructions: planData.instructions || null,
                    planInfo: planData.planInfo || null,
                    additionalInfo: planData.additionalInfo || null,
                    // Set status based on provider's current visibility
                    status: providerPlansHidden ? 'hidden' : 'visible',
                    isActive: true,
                    createdAt: planTimestamp,
                    updatedAt: planTimestamp
                };

                // Create the plan
                const plan = await EsimPlan.create(processedPlanData);

                // Associate countries with the plan (use converted country IDs)
                await Promise.all(countryIds.map(countryId =>
                    plan.addCountry(countryId, {
                        through: {
                            id: uuidv4(),
                            isDefault: countryId === countryIds[0] // First country as default
                        }
                    })
                ));

                // Fetch the created plan with associations
                const createdPlan = await EsimPlan.findByPk(plan.id, {
                    include: [
                        {
                            model: Country,
                            as: 'countries',
                            through: { attributes: ['isDefault'] }
                        },
                        {
                            model: Provider,
                            as: 'provider',
                            attributes: ['id', 'name', 'type', 'country']
                        }
                    ]
                });

                createdPlans.push(createdPlan);

            } catch (error) {
                console.error(`Error creating plan at row ${rowIndex}:`, error);
                errors.push({
                    row: rowIndex,
                    error: error.message || 'Failed to create plan'
                });
            }
        }

        // Return results
        const response = {
            message: `Bulk upload completed. Created: ${createdPlans.length}, Errors: ${errors.length}`,
            plans: createdPlans,
            errors: errors,
            summary: {
                total: plansData.length,
                created: createdPlans.length,
                failed: errors.length
            }
        };

        if (errors.length > 0) {
            return res.status(207).json(response); // 207 Multi-Status for partial success
        }

        res.status(201).json(response);

    } catch (error) {
        console.error('Error in bulk plan creation:', error);
        res.status(500).json({
            message: 'Failed to process bulk plan creation',
            error: error.message
        });
    }
};

// Update eSIM plan
exports.updateEsimPlan = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedPlan = await EsimPlan.findByPk(id);

        if (!updatedPlan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        // Update plan fields
        await updatedPlan.update(req.body);

        // Handle country associations if provided
        if (req.body.countries) {
            await updatedPlan.setCountries(req.body.countries);
        }

        // Invalidate cache for this plan
        invalidatePlanCache(id);

        // Fetch the updated plan with its associations
        const plan = await EsimPlan.findByPk(id, {
            include: [{
                model: Country,
                as: 'countries',
                through: { attributes: [] }
            }]
        });

        res.json(plan);
    } catch (error) {
        console.error('Error updating eSIM plan:', error);
        res.status(500).json({ message: 'Failed to update eSIM plan' });
    }
};

// Delete eSIM plan
exports.deleteEsimPlan = async (req, res) => {
    try {
        const plan = await EsimPlan.findByPk(req.params.id);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        await plan.destroy();
        res.json({ message: 'eSIM plan deleted successfully' });
    } catch (error) {
        console.error('Error deleting eSIM plan:', error);
        res.status(500).json({ message: 'Failed to delete eSIM plan' });
    }
};

// Update eSIM plan status
exports.updateEsimPlanStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        await plan.update({ status });

        // Invalidate all caches since plan visibility affects multiple views
        invalidatePlanCache(null);

        // Emit WebSocket event for real-time updates
        const websocketService = req.app.get('websocketService');
        if (websocketService) {
            websocketService.broadcastPlanVisibilityChange({
                type: 'single',
                planId: id,
                status: status
            });
        }

        res.json({ message: 'Plan status updated successfully' });
    } catch (error) {
        console.error('Error updating plan status:', error);
        res.status(500).json({ message: 'Failed to update plan status' });
    }
};

// Update all eSIM plans visibility for a specific provider
exports.updateProviderPlansVisibility = async (req, res) => {
    try {
        const { providerId } = req.params;
        const { status } = req.body;

        // Validate status
        if (!['visible', 'hidden'].includes(status)) {
            return res.status(400).json({ message: 'Invalid status. Must be "visible" or "hidden"' });
        }

        // Check if provider exists
        const provider = await Provider.findByPk(providerId);
        if (!provider) {
            return res.status(404).json({ message: 'Provider not found' });
        }

        // Update all plans for this provider
        const [updatedCount] = await EsimPlan.update(
            { status },
            {
                where: {
                    providerId: providerId,
                    isActive: true
                }
            }
        );

        // Invalidate cache for all plans (since we updated multiple plans)
        // Note: In a production environment, you might want to implement more granular cache invalidation
        if (global.planCache) {
            global.planCache.clear();
        }

        // Emit WebSocket event for real-time updates
        const websocketService = req.app.get('websocketService');
        if (websocketService) {
            websocketService.broadcastPlanVisibilityChange({
                type: 'provider',
                providerId: providerId,
                status: status
            });
        }

        res.json({
            message: `Successfully updated ${updatedCount} plans to ${status} status`,
            updatedCount
        });
    } catch (error) {
        console.error('Error updating provider plans visibility:', error);
        res.status(500).json({ message: 'Failed to update provider plans visibility' });
    }
};

// Update eSIM plan selling price
exports.updateEsimPlanPrice = async (req, res) => {
    try {
        const { id } = req.params;
        const { sellingPrice } = req.body;

        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'Plan not found' });
        }

        await plan.update({ sellingPrice });

        // Invalidate cache for this plan
        invalidatePlanCache(id);

        res.json({ message: 'Plan price updated successfully' });
    } catch (error) {
        console.error('Error updating plan price:', error);
        res.status(500).json({ message: 'Failed to update plan price' });
    }
};

// Reset eSIM plan selling price
exports.resetEsimPlanPrice = async (req, res) => {
    try {
        const plan = await EsimPlan.findByPk(req.params.id);

        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        await plan.update({ sellingPrice: null });

        res.json({ message: 'Selling price has been reset successfully' });
    } catch (error) {
        console.error('Error resetting eSIM plan price:', error);
        res.status(500).json({ message: 'Failed to reset eSIM plan price' });
    }
};

// Get eSIM plans for partners - OPTIMIZED VERSION
exports.getPartnerEsimPlans = async (req, res) => {
    try {
        const { search, countryId, region, category = 'esim_realtime', page = 1, limit = 24 } = req.query;
        const partnerId = req.user.id;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const offset = (pageNum - 1) * limitNum;

        // Create cache key for this specific request
        const cacheKey = `partner_${partnerId}_plans_${JSON.stringify({
            search, countryId, region, category, page, limit
        })}`;

        // Build base where clause
        let whereClause = {
            status: 'visible',
            isActive: true,
            category
        };

        // Add search filter if provided
        if (search) {
            whereClause[Op.or] = [
                { name: { [Op.like]: `%${search}%` } },
                { networkName: { [Op.like]: `%${search}%` } }
            ];
        }

        // Add region filter if provided
        if (region && region !== 'all') {
            whereClause.region = { [Op.like]: `%${region}%` };
        }

        // Include provider and check provider status
        const include = [
            {
                model: Provider,
                as: 'provider',
                where: { status: 'active' }, // Only include plans from active providers
                required: true
            }
        ];

        // Add country filter if provided
        if (countryId && countryId !== 'all') {
            include.push({
                model: Country,
                as: 'countries',
                where: { id: countryId },
                required: true
            });
        } else {
            include.push({
                model: Country,
                as: 'countries'
            });
        }

        // Get partner for markup calculation
        const partner = await User.findByPk(partnerId);
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        // Get total count for pagination
        const { count: totalItems } = await EsimPlan.findAndCountAll({
            where: whereClause,
            include,
            distinct: true
        });

        const totalPages = Math.ceil(totalItems / limitNum);

        // Get plans with pagination
        let allPlans = await EsimPlan.findAll({
            where: whereClause,
            include,
            order: [['createdAt', 'DESC']],
            limit: limitNum,
            offset,
            distinct: true
        });

        // Apply markup-based pricing for all plans
        const processedPlans = allPlans.map(plan => {
            const planData = {
                ...(plan.toJSON ? plan.toJSON() : plan)
            };

            // Calculate display price based on markup
            if (!planData.sellingPrice) {
                const markup = partner.markupPercentage || 0;
                const basePrice = parseFloat(planData.buyingPrice);
                const markupAmount = (basePrice * markup) / 100;
                planData.displayPrice = (basePrice + markupAmount).toFixed(2);
            } else {
                planData.displayPrice = planData.sellingPrice;
            }

            return planData;
        });

        // Get all unique regions from active plans
        const regions = await EsimPlan.findAll({
            attributes: ['region'],
            where: {
                status: 'visible',
                isActive: true,
                region: { [Op.not]: null },
                '$provider.status$': 'active'  // Only include regions from active providers
            },
            include: [{
                model: Provider,
                as: 'provider',
                attributes: [],
                required: true
            }]
        });

        // Process and deduplicate regions
        const processedRegions = regions
            .map(r => r.region)
            .filter(Boolean)
            .flatMap(region => 
                typeof region === 'string' 
                    ? region.split(',').map(r => r.trim())
                    : Array.isArray(region) 
                        ? region.map(r => r.trim())
                        : []
            )
            .filter((value, index, self) => value && self.indexOf(value) === index)
            .sort();

        // Get all countries
        const countries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3'],
            order: [['name', 'ASC']]
        });

        const responseData = {
            plans: processedPlans,
            page: pageNum,
            totalPages,
            totalItems,
            countries,
            regions: processedRegions
        };

        // Cache the response
        setCachePlan(cacheKey, responseData, 300);

        res.json(responseData);
    } catch (error) {
        console.error('Error getting partner eSIM plans:', error);
        res.status(500).json({
            message: 'Failed to get eSIM plans',
            error: error.message
        });
    }
};

// Removed unused cache variables - using getCachedPlan/setCachePlan functions instead

exports.getPartnerEsimPlan = async (req, res) => {
    try {
        const { id } = req.params;
        const partnerId = req.user.id;

        // Create cache key for this specific request
        const cacheKey = `partner_${partnerId}_plan_${id}`;

        // Get partner for markup calculation
        const partner = await User.findByPk(partnerId);
        if (!partner) {
            return res.status(404).json({ message: 'Partner not found' });
        }

        // Get plan with provider and country information
        let plan = await EsimPlan.findOne({
            where: {
                [Op.or]: [
                    { id },
                    { productId: id },
                    { externalProductId: id }
                ],
                status: 'visible',
                isActive: true
            },
            include: [
                {
                    model: Provider,
                    as: 'provider',
                    where: { status: 'active' }, // Only show plans from active providers
                    required: true
                },
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: ['isDefault'] }
                }
            ]
        });

        if (!plan) {
            return res.status(404).json({ message: 'Plan not found or not available' });
        }

        // Get stock count
        const stockCount = plan.provider?.name === 'mobimatter' ? 999 : await sequelize.models.EsimStock.count({
            where: {
                esimPlanId: plan.id,
                status: 'available'
            }
        });

        plan = plan.toJSON();
        plan.stockCount = stockCount;

        // Apply markup-based pricing if no specific selling price
        if (!plan.sellingPrice && partner.markupPercentage) {
            const markup = 1 + (partner.markupPercentage / 100);
            plan.calculatedPrice = Number((plan.buyingPrice * markup).toFixed(2));
            plan.sellingPrice = null;
        }

        // For response, use calculatedPrice if available, otherwise use sellingPrice
        plan.displayPrice = plan.calculatedPrice || plan.sellingPrice;

        // Cache the plan data
        setCachePlan(cacheKey, plan);

        res.json(plan);
    } catch (error) {
        console.error('Error getting partner eSIM plan:', error);
        res.status(500).json({ message: 'Failed to get eSIM plan' });
    }
};

// Toggle start date requirement
exports.toggleStartDate = async (req, res) => {
    try {
        const { id } = req.params;

        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        // Toggle the startDateEnabled field
        plan.startDateEnabled = !plan.startDateEnabled;

        // Set activation policy to 'Activation upon travel date' when start date is enabled
        if (plan.startDateEnabled) {
            plan.activationPolicy = 'Activation upon travel date';
        }

        await plan.save();

        res.json({
            message: 'Start date requirement updated successfully',
            startDateEnabled: plan.startDateEnabled,
            activationPolicy: plan.activationPolicy
        });
    } catch (error) {
        console.error('Error toggling start date:', error);
        res.status(500).json({ message: 'Failed to update start date requirement' });
    }
};

// Update stock threshold for eSIM plan
exports.updateStockThreshold = async (req, res) => {
    try {
        const { id } = req.params;
        const { threshold } = req.body;

        // Validate threshold
        const newThreshold = parseInt(threshold);
        if (isNaN(newThreshold) || newThreshold < 1 || newThreshold > 1000) {
            return res.status(400).json({
                message: 'Invalid threshold value. Must be between 1 and 1000.'
            });
        }

        // Find the plan
        const plan = await EsimPlan.findByPk(id);
        if (!plan) {
            return res.status(404).json({ message: 'eSIM plan not found' });
        }

        // Get current stock count
        const stockCount = await sequelize.models.EsimStock.count({
            where: {
                esimPlanId: id,
                status: 'available'
            }
        });

        // Update threshold
        await plan.update({
            stockThreshold: newThreshold
        });

        // Check if current stock is below new threshold
        const isLowStock = stockCount < newThreshold;

        // If stock is below threshold, send notification
        if (isLowStock) {
            await checkStockAndNotify(id);
        }

        res.json({
            message: 'Stock threshold updated successfully',
            stockThreshold: newThreshold,
            currentStock: stockCount,
            isLowStock
        });
    } catch (error) {
        console.error('Error updating stock threshold:', error);
        res.status(500).json({
            message: 'Failed to update stock threshold',
            error: error.message
        });
    }
};

// Sync external plans from providers
exports.syncExternalPlans = async (req, res) => {
    try {
        // Return immediately and run sync in background
        res.json({
            message: 'Sync operation started in background',
            status: 'started',
            timestamp: new Date().toISOString()
        });

        // Get the io instance for WebSocket notifications
        const io = req.app.get('io');

        // Run the actual sync operation in background
        performSyncOperation(io).catch(error => {
            console.error('[Sync] Background sync failed:', error);
            if (io) {
                io.emit('sync_error', {
                    message: 'Sync operation failed',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

    } catch (error) {
        console.error('[Sync] Error starting sync operation:', error);
        res.status(500).json({
            message: 'Failed to start sync operation',
            error: error.message
        });
    }
};

// Separate function to perform the actual sync operation
async function performSyncOperation(io) {
    try {
        // Emit sync start event
        if (io) {
            io.emit('sync_progress', {
                phase: 'starting',
                message: 'Starting sync operation...',
                progress: 0
            });
        }

        // Get active external providers
        const providers = await Provider.findAll({
            where: {
                type: 'API',
                status: 'active',
                name: {
                    [Op.in]: ['mobimatter', 'billionconnect']
                }
            }
        });

        const results = {
            total: 0,
            updated: 0,
            created: 0,
            failed: 0,
            inactive: 0,
            metadataUpdated: 0,
            errors: []
        };

        // Get all countries for mapping
        const allCountries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map of country names and ISO3 codes to IDs for quick lookup
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        // Categories to sync
        const categories = ['esim_realtime', 'esim_addon', 'esim_replacement'];

        // Emit progress update
        if (io) {
            io.emit('sync_progress', {
                phase: 'preparation',
                message: 'Preparing sync operation...',
                progress: 5
            });
        }

        // STEP 1: Clear cache to ensure partners get fresh data
        // Instead of directly accessing planCache, use the invalidatePlanCache utility
        // with null to invalidate all plans
        invalidatePlanCache(null);
        console.log('[Sync] Cleared plan cache');

        // STEP 2: Mark all external provider plans as inactive before syncing
        await EsimPlan.update(
            { isActive: false },
            {
                where: {
                    providerId: providers.map(p => p.id)
                }
            }
        );
        console.log('[Sync] Marked all external plans as inactive');

        // Emit progress update
        if (io) {
            io.emit('sync_progress', {
                phase: 'fetching',
                message: 'Fetching plans from external providers...',
                progress: 10
            });
        }

        // Keep track of all fetched external plan IDs
        const fetchedExternalPlanIds = new Set();
        // Track all plans that were updated or created for metadata processing
        const processedPlanIds = [];

        let totalCategories = providers.length * categories.length;
        let processedCategories = 0;

        for (const provider of providers) {
            try {
                const providerService = providerFactory.getProvider(provider.name);

                for (const category of categories) {
                    try {
                        console.log(`[Sync] Fetching ${category} plans from ${provider.name}...`);

                        // Emit progress update for each category
                        if (io) {
                            io.emit('sync_progress', {
                                phase: 'fetching',
                                message: `Fetching ${category} plans from ${provider.name}...`,
                                progress: 10 + (processedCategories / totalCategories) * 40
                            });
                        }

                        // Add a delay between API calls to prevent rate limiting
                        await delay(1000); // 1 second delay

                        const externalPlans = await providerService.getProducts(category);

                        if (!Array.isArray(externalPlans)) {
                            throw new Error(`Invalid response from provider ${provider.name} for category ${category}`);
                        }

                        console.log(`[Sync] Found ${externalPlans.length} ${category} plans from ${provider.name}`);

                        processedCategories++;

                        for (const [index, externalPlan] of externalPlans.entries()) {
                            try {
                                // Add a small delay every 5 plans to avoid database connection issues
                                if (index > 0 && index % 5 === 0) {
                                    await delay(300); // 300ms delay
                                }

                                results.total++;
                                const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);

                                // Add external plan ID to tracking set
                                if (standardizedPlan.externalProductId) {
                                    fetchedExternalPlanIds.add(standardizedPlan.externalProductId);
                                }
                                if (standardizedPlan.externalSkuId) {
                                    fetchedExternalPlanIds.add(standardizedPlan.externalSkuId);
                                }

                                // Ensure the category is set correctly
                                standardizedPlan.category = category;

                                // Check if plan already exists
                                let plan = await EsimPlan.findOne({
                                    where: {
                                        [Op.or]: [
                                            { externalProductId: standardizedPlan.externalProductId },
                                            { externalSkuId: standardizedPlan.externalSkuId }
                                        ]
                                    }
                                });

                                if (plan) {
                                    // Fields to preserve from the existing plan
                                    const fieldsToPreserve = {
                                        sellingPrice: plan.sellingPrice,
                                        status: plan.status,
                                        stockThreshold: plan.stockThreshold,
                                        startDateEnabled: plan.startDateEnabled,
                                        features: plan.features,
                                        instructions: plan.instructions,
                                        description: plan.description,
                                        activationPolicy: plan.activationPolicy,
                                        hotspot: plan.hotspot,
                                        speed: plan.speed,
                                        top_up: plan.top_up
                                    };

                                    // Update existing plan while preserving manual fields and setting isActive to true
                                    await plan.update({
                                        ...standardizedPlan,
                                        ...fieldsToPreserve,
                                        providerId: provider.id,
                                        isActive: true // Set the plan to active since it's in the current provider data
                                    });
                                    results.updated++;
                                    processedPlanIds.push(plan.id);
                                } else {
                                    // Check if provider's plans are currently hidden
                                    const providerPlansHidden = await isProviderPlansHidden(provider.id);

                                    // Create new plan with isActive = true
                                    plan = await EsimPlan.create({
                                        ...standardizedPlan,
                                        providerId: provider.id,
                                        productId: await generateProductId(),
                                        // Set status based on provider's current visibility
                                        status: providerPlansHidden ? 'hidden' : 'visible',
                                        isActive: true // New plans are active by default
                                    });
                                    results.created++;
                                    processedPlanIds.push(plan.id);
                                }

                                // Get supported countries from the plan
                                const supportedCountries =
                                    externalPlan.countries ||
                                    externalPlan.supportedCountries ||
                                    (externalPlan.providerMetadata?.originalData?.countries || []);

                                // Clear existing country associations
                                await plan.setCountries([]);

                                // Add new country associations
                                if (Array.isArray(supportedCountries)) {
                                    const countryAssociations = [];
                                    supportedCountries.forEach(countryCode => {
                                        const country = countryMap.get(countryCode.toLowerCase());
                                        if (country) {
                                            countryAssociations.push({
                                                countryId: country.id,
                                                isDefault: countryAssociations.length === 0
                                            });
                                        }
                                    });

                                    // Add all countries at once
                                    if (countryAssociations.length > 0) {
                                        await plan.addCountries(
                                            countryAssociations.map(assoc => assoc.countryId),
                                            {
                                                through: countryAssociations.map(assoc => ({
                                                    isDefault: assoc.isDefault,
                                                    id: uuidv4()
                                                }))
                                            }
                                        );
                                    }
                                }

                            } catch (error) {
                                console.error(`[Sync] Error processing plan ${externalPlan.id}:`, error);
                                results.failed++;
                                results.errors.push({
                                    planId: externalPlan.id,
                                    error: error.message
                                });
                            }
                        }
                    } catch (error) {
                        console.error(`[Sync] Error fetching ${category} plans from ${provider.name}:`, error);
                        results.errors.push({
                            provider: provider.name,
                            category,
                            error: error.message
                        });
                    }
                }
            } catch (error) {
                console.error(`[Sync] Error processing provider ${provider.name}:`, error);
                results.errors.push({
                    provider: provider.name,
                    error: error.message
                });
            }
        }

        // Count how many plans remain inactive after sync
        const inactivePlans = await EsimPlan.count({
            where: {
                providerId: providers.map(p => p.id),
                isActive: false
            }
        });
        results.inactive = inactivePlans;

        // STEP 3: Run metadata update process on all plans that were created or updated
        console.log(`[Sync] Running metadata update for ${processedPlanIds.length} plans...`);
        if (processedPlanIds.length > 0) {
            try {
                // Find all processed plans
                const plansToUpdate = await EsimPlan.findAll({
                    where: {
                        id: {
                            [Op.in]: processedPlanIds
                        },
                        providerMetadata: {
                            [Op.not]: null
                        }
                    }
                });

                console.log(`[Sync] Found ${plansToUpdate.length} plans with provider metadata to update`);

                for (const [index, plan] of plansToUpdate.entries()) {
                    try {
                        // Add a small delay every 10 plans to avoid database connection issues
                        if (index > 0 && index % 10 === 0) {
                            await delay(500); // 500ms delay
                        }

                        const metadata = plan.providerMetadata;
                        let planInfoContent = '';
                        let additionalInfoContent = '';

                        // Extract plan details from customData
                        if (metadata.customData) {
                            const planDetails = metadata.customData.find(d => d.name?.trim() === 'PLAN_DETAILS');
                            if (planDetails?.value) {
                                try {
                                    const parsed = JSON.parse(planDetails.value);

                                    // Store description in planInfo
                                    if (parsed.description) {
                                        planInfoContent += `<div>${parsed.description}</div>`;
                                    }

                                    // Store key features in planInfo
                                    if (parsed.items && Array.isArray(parsed.items) && parsed.items.length > 0) {
                                        planInfoContent += `
                                        <div class="mt-4">
                                            <h4 class="text-sm font-semibold mb-2">Key Features:</h4>
                                            <ul class="list-disc list-inside space-y-1">
                                                ${parsed.items.map(item => `<li>${item}</li>`).join('\n')}
                                            </ul>
                                        </div>`;
                                    }
                                } catch (e) {
                                    console.error(`[Sync] Error parsing PLAN_DETAILS for plan ${plan.id}:`, e.message);
                                }
                            }
                        }

                        // Extract additional information like usage tracking
                        if (metadata.usageTracking) {
                            additionalInfoContent += `
                            <div class="mt-4">
                                <h4 class="text-sm font-semibold mb-2">Usage Tracking:</h4>
                                <p>${metadata.usageTracking}</p>
                            </div>`;
                        }

                        // Extract any other useful information from metadata
                        if (metadata.productDetails && Array.isArray(metadata.productDetails)) {
                            const heading = metadata.productDetails.find(detail => detail.name === "heading")?.value;
                            if (heading) {
                                additionalInfoContent += `
                                <div class="mt-4">
                                    <h4 class="text-sm font-semibold mb-2">Product Details:</h4>
                                    <p>${heading}</p>
                                </div>`;
                            }

                            // Add other product details that might be useful
                            metadata.productDetails
                                .filter(detail =>
                                    detail.name !== "heading" &&
                                    detail.name !== "PLAN_DATA_LIMIT" &&
                                    detail.value)
                                .forEach(detail => {
                                    additionalInfoContent += `
                                    <div class="mt-2">
                                        <strong>${detail.name}:</strong> ${detail.value}
                                    </div>`;
                                });
                        }

                        // Extract original data if available
                        if (metadata.originalData) {
                            if (metadata.originalData.additionalDetails) {
                                additionalInfoContent += `
                                <div class="mt-4">
                                    <h4 class="text-sm font-semibold mb-2">Additional Details:</h4>
                                    <p>${metadata.originalData.additionalDetails}</p>
                                </div>`;
                            }
                        }

                        // Update the plan only if we have content to add
                        if (planInfoContent || additionalInfoContent) {
                            await plan.update({
                                planInfo: planInfoContent || plan.planInfo,
                                additionalInfo: additionalInfoContent || plan.additionalInfo
                            });
                            results.metadataUpdated++;
                            console.log(`[Sync] Updated metadata for plan: ${plan.id} - ${plan.name}`);
                        }
                    } catch (error) {
                        console.error(`[Sync] Error updating metadata for plan ${plan.id}:`, error);
                    }
                }

                console.log(`[Sync] Metadata update completed. Updated ${results.metadataUpdated} plans.`);

                // Emit progress update for metadata completion
                if (io) {
                    io.emit('sync_progress', {
                        phase: 'metadata',
                        message: `Metadata update completed. Updated ${results.metadataUpdated} plans.`,
                        progress: 95
                    });
                }
            } catch (error) {
                console.error('[Sync] Error updating plan metadata:', error);
                results.errors.push({
                    phase: 'metadata_update',
                    error: error.message
                });
            }
        }

        // Emit completion notification
        if (io) {
            io.emit('sync_complete', {
                message: 'Sync operation completed successfully',
                results: results,
                timestamp: new Date().toISOString()
            });
        }

        console.log('[Sync] Operation completed:', results);

    } catch (error) {
        console.error('[Sync] Error in background sync operation:', error);

        // Emit error notification
        if (io) {
            io.emit('sync_error', {
                message: 'Sync operation failed',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }

        throw error; // Re-throw to be caught by the calling function
    }
}

// Sync BillionConnect plans specifically
exports.syncBillionConnectPlans = async (req, res) => {
    try {
        // Return immediately and run sync in background
        res.json({
            message: 'BillionConnect sync operation started in background',
            status: 'started',
            timestamp: new Date().toISOString()
        });

        // Get the io instance for WebSocket notifications
        const io = req.app.get('io');

        // Run the actual sync operation in background
        performBillionConnectSyncOperation(io).catch(error => {
            console.error('[BillionConnect Sync] Background sync failed:', error);
            if (io) {
                io.emit('billionconnect_sync_error', {
                    message: 'BillionConnect sync operation failed',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

    } catch (error) {
        console.error('Error starting BillionConnect sync:', error);
        res.status(500).json({
            message: 'Failed to start BillionConnect sync operation',
            error: error.message
        });
    }
};

// Separate function to perform BillionConnect sync operation
async function performBillionConnectSyncOperation(io) {
    try {
        // Emit sync start event
        if (io) {
            io.emit('billionconnect_sync_progress', {
                phase: 'starting',
                message: 'Starting BillionConnect sync operation...',
                progress: 0
            });
        }

        // Get BillionConnect provider
        const provider = await Provider.findOne({
            where: {
                type: 'API',
                status: 'active',
                name: 'billionconnect'
            }
        });

        if (!provider) {
            throw new Error('BillionConnect provider not found or not active');
        }

        const results = {
            total: 0,
            updated: 0,
            created: 0,
            failed: 0,
            inactive: 0,
            errors: []
        };

        // Get all countries for mapping
        const allCountries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map of country names and ISO3 codes to IDs for quick lookup
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        // Emit progress update
        if (io) {
            io.emit('billionconnect_sync_progress', {
                phase: 'fetching',
                message: 'Fetching plans from BillionConnect API...',
                progress: 10
            });
        }

        // Mark all BillionConnect plans as inactive before syncing
        await EsimPlan.update(
            { isActive: false },
            {
                where: {
                    providerId: provider.id
                }
            }
        );
        console.log('[BillionConnect Sync] Marked all BillionConnect plans as inactive');

        try {
            const providerService = providerFactory.getProvider(provider.name);

            // Get products from BillionConnect with prices
            const externalPlans = await providerService.getProductsWithPrices();

            if (!Array.isArray(externalPlans)) {
                throw new Error('Invalid response from BillionConnect API');
            }

            console.log(`[BillionConnect Sync] Found ${externalPlans.length} plans from BillionConnect`);

            // Emit progress update
            if (io) {
                io.emit('billionconnect_sync_progress', {
                    phase: 'processing',
                    message: `Processing ${externalPlans.length} BillionConnect plans...`,
                    progress: 30
                });
            }

            for (const [index, externalPlan] of externalPlans.entries()) {
                try {
                    results.total++;
                    const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);

                    // Set category to esim_realtime for BillionConnect plans
                    standardizedPlan.category = 'esim_realtime';

                    // Check if plan already exists
                    let plan = await EsimPlan.findOne({
                        where: {
                            [Op.or]: [
                                { externalProductId: standardizedPlan.externalProductId },
                                { externalSkuId: standardizedPlan.externalSkuId }
                            ]
                        }
                    });

                    if (plan) {
                        // Fields to preserve from the existing plan
                        const fieldsToPreserve = {
                            sellingPrice: plan.sellingPrice,
                            status: plan.status,
                            stockThreshold: plan.stockThreshold,
                            startDateEnabled: plan.startDateEnabled,
                            features: plan.features,
                            instructions: plan.instructions
                        };

                        // Update existing plan while preserving manual fields and setting isActive to true
                        await plan.update({
                            ...standardizedPlan,
                            ...fieldsToPreserve,
                            providerId: provider.id,
                            isActive: true
                        });
                        results.updated++;
                    } else {
                        // Check if provider's plans are currently hidden
                        const providerPlansHidden = await isProviderPlansHidden(provider.id);

                        // Create new plan with isActive = true
                        plan = await EsimPlan.create({
                            ...standardizedPlan,
                            providerId: provider.id,
                            productId: await generateProductId(),
                            // Set status based on provider's current visibility
                            status: providerPlansHidden ? 'hidden' : 'visible',
                            isActive: true
                        });
                        results.created++;
                    }

                    // Handle country associations
                    const supportedCountries = standardizedPlan.supportedCountries || [];

                    // Clear existing country associations
                    await plan.setCountries([]);

                    // Add new country associations
                    if (Array.isArray(supportedCountries) && supportedCountries.length > 0) {
                        const countryAssociations = [];
                        supportedCountries.forEach(countryCode => {
                            const country = countryMap.get(countryCode.toLowerCase());
                            if (country) {
                                countryAssociations.push({
                                    countryId: country.id,
                                    isDefault: countryAssociations.length === 0
                                });
                            }
                        });

                        // Add all countries at once
                        if (countryAssociations.length > 0) {
                            await plan.addCountries(
                                countryAssociations.map(assoc => assoc.countryId),
                                {
                                    through: countryAssociations.map(assoc => ({
                                        isDefault: assoc.isDefault,
                                        id: uuidv4()
                                    }))
                                }
                            );
                        }
                    }

                    // Emit progress update every 10 plans
                    if (index > 0 && index % 10 === 0) {
                        const progress = 30 + (index / externalPlans.length) * 60;
                        if (io) {
                            io.emit('billionconnect_sync_progress', {
                                phase: 'processing',
                                message: `Processed ${index + 1}/${externalPlans.length} BillionConnect plans...`,
                                progress: Math.round(progress)
                            });
                        }
                    }

                } catch (error) {
                    console.error(`[BillionConnect Sync] Error processing plan ${externalPlan.id}:`, error);
                    results.failed++;
                    results.errors.push({
                        planId: externalPlan.id,
                        error: error.message
                    });
                }
            }

        } catch (error) {
            console.error('[BillionConnect Sync] Error fetching plans from BillionConnect:', error);
            results.errors.push({
                provider: provider.name,
                error: error.message
            });
        }

        // Count how many plans remain inactive after sync
        const inactivePlans = await EsimPlan.count({
            where: {
                providerId: provider.id,
                isActive: false
            }
        });
        results.inactive = inactivePlans;

        // Clear cache to ensure partners get fresh data
        invalidatePlanCache(null);
        console.log('[BillionConnect Sync] Cleared plan cache');

        // Emit completion notification
        if (io) {
            io.emit('billionconnect_sync_complete', {
                message: 'BillionConnect sync operation completed successfully',
                results: results,
                timestamp: new Date().toISOString()
            });
        }

        console.log('[BillionConnect Sync] Operation completed:', results);

    } catch (error) {
        console.error('[BillionConnect Sync] Error in background sync operation:', error);

        // Emit error notification
        if (io) {
            io.emit('billionconnect_sync_error', {
                message: 'BillionConnect sync operation failed',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }

        throw error;
    }
}

// Sync Mobimatter plans
exports.syncMobimatterPlans = async (req, res) => {
    try {
        // Get the WebSocket instance
        const io = req.app.get('io');

        // Start the sync operation in the background
        performMobimatterSyncOperation(io).catch(error => {
            console.error('[Sync] Error syncing Mobimatter plans:', error);
            if (io) {
                io.emit('sync_error', {
                    message: 'Sync operation failed',
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Return success response immediately
        res.json({
            message: 'Mobimatter plans sync operation started',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('[Sync] Error starting Mobimatter sync operation:', error);
        res.status(500).json({
            message: 'Failed to start Mobimatter sync operation',
            error: error.message
        });
    }
};

// Separate function to perform the Mobimatter sync operation
async function performMobimatterSyncOperation(io) {
    try {
        // Emit sync start event
        if (io) {
            io.emit('sync_progress', {
                phase: 'starting',
                message: 'Starting Mobimatter sync operation...',
                progress: 0
            });
        }

        // Get Mobimatter provider
        const provider = await Provider.findOne({
            where: {
                type: 'API',
                status: 'active',
                name: 'mobimatter'
            }
        });

        if (!provider) {
            throw new Error('Mobimatter provider not found or not active');
        }

        const results = {
            total: 0,
            updated: 0,
            created: 0,
            failed: 0,
            inactive: 0,
            metadataUpdated: 0,
            errors: []
        };

        // Get all countries for mapping
        const allCountries = await Country.findAll({
            where: { isActive: true },
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map of country names and ISO3 codes to IDs for quick lookup
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        // Categories to sync
        const categories = ['esim_realtime', 'esim_addon', 'esim_replacement'];

        // Emit progress update
        if (io) {
            io.emit('sync_progress', {
                phase: 'preparation',
                message: 'Preparing Mobimatter sync operation...',
                progress: 5
            });
        }

        // Clear cache for Mobimatter plans
        invalidatePlanCache(provider.id);
        console.log('[Sync] Cleared Mobimatter plan cache');

        // Mark all Mobimatter plans as inactive before syncing
        await EsimPlan.update(
            { isActive: false },
            {
                where: {
                    providerId: provider.id
                }
            }
        );
        console.log('[Sync] Marked all Mobimatter plans as inactive');

        // Emit progress update
        if (io) {
            io.emit('sync_progress', {
                phase: 'fetching',
                message: 'Fetching plans from Mobimatter...',
                progress: 10
            });
        }

        // Keep track of all fetched external plan IDs
        const fetchedExternalPlanIds = new Set();
        // Track all plans that were updated or created for metadata processing
        const processedPlanIds = [];

        let totalCategories = categories.length;
        let processedCategories = 0;

        const providerService = providerFactory.getProvider(provider.name);

        for (const category of categories) {
            try {
                console.log(`[Sync] Fetching ${category} plans from Mobimatter...`);

                // Emit progress update for each category
                if (io) {
                    io.emit('sync_progress', {
                        phase: 'fetching',
                        message: `Fetching ${category} plans from Mobimatter...`,
                        progress: 10 + (processedCategories / totalCategories) * 40
                    });
                }

                // Add a delay between API calls to prevent rate limiting
                await delay(1000); // 1 second delay

                const externalPlans = await providerService.getProducts(category);

                if (!Array.isArray(externalPlans)) {
                    throw new Error(`Invalid response from Mobimatter for category ${category}`);
                }

                console.log(`[Sync] Found ${externalPlans.length} ${category} plans from Mobimatter`);

                processedCategories++;

                for (const [index, externalPlan] of externalPlans.entries()) {
                    try {
                        // Add a small delay every 5 plans to avoid database connection issues
                        if (index > 0 && index % 5 === 0) {
                            await delay(300); // 300ms delay
                        }

                        results.total++;
                        const standardizedPlan = await providerFactory.standardizeProduct(provider.name, externalPlan);

                        // Add external plan ID to tracking set
                        if (standardizedPlan.externalProductId) {
                            fetchedExternalPlanIds.add(standardizedPlan.externalProductId);
                        }
                        if (standardizedPlan.externalSkuId) {
                            fetchedExternalPlanIds.add(standardizedPlan.externalSkuId);
                        }

                        // Ensure the category is set correctly
                        standardizedPlan.category = category;

                        // Check if plan already exists
                        let plan = await EsimPlan.findOne({
                            where: {
                                [Op.or]: [
                                    { externalProductId: standardizedPlan.externalProductId },
                                    { externalSkuId: standardizedPlan.externalSkuId }
                                ]
                            }
                        });

                        if (plan) {
                            // Fields to preserve from the existing plan
                            const fieldsToPreserve = {
                                sellingPrice: plan.sellingPrice,
                                status: plan.status,
                                stockThreshold: plan.stockThreshold,
                                startDateEnabled: plan.startDateEnabled,
                                features: plan.features,
                                instructions: plan.instructions,
                                description: plan.description,
                                activationPolicy: plan.activationPolicy,
                                hotspot: plan.hotspot,
                                speed: plan.speed,
                                top_up: plan.top_up
                            };

                            // Update existing plan while preserving manual fields and setting isActive to true
                            await plan.update({
                                ...standardizedPlan,
                                ...fieldsToPreserve,
                                providerId: provider.id,
                                isActive: true
                            });
                            results.updated++;
                            processedPlanIds.push(plan.id);
                        } else {
                            // Check if provider's plans are currently hidden
                            const providerPlansHidden = await isProviderPlansHidden(provider.id);

                            // Create new plan with isActive = true
                            plan = await EsimPlan.create({
                                ...standardizedPlan,
                                providerId: provider.id,
                                // Set status based on provider's current visibility
                                status: providerPlansHidden ? 'hidden' : 'visible',
                                isActive: true
                            });
                            results.created++;
                            processedPlanIds.push(plan.id);
                        }

                        // Emit progress update
                        if (io && index % 10 === 0) {
                            io.emit('sync_progress', {
                                phase: 'processing',
                                message: `Processing ${category} plans (${index + 1}/${externalPlans.length})...`,
                                progress: 50 + (processedCategories / totalCategories) * 40
                            });
                        }

                    } catch (error) {
                        console.error(`[Sync] Error processing plan:`, error);
                        results.failed++;
                        results.errors.push({
                            plan: externalPlan,
                            error: error.message
                        });
                    }
                }

            } catch (error) {
                console.error(`[Sync] Error fetching ${category} plans from Mobimatter:`, error);
                results.errors.push({
                    category,
                    error: error.message
                });
            }
        }

        // Emit completion event
        if (io) {
            io.emit('sync_complete', {
                message: 'Mobimatter plans sync completed',
                results,
                timestamp: new Date().toISOString()
            });
        }

        console.log('[Sync] Mobimatter sync operation completed:', results);
        return results;

    } catch (error) {
        console.error('[Sync] Error in Mobimatter sync operation:', error);
        throw error;
    }
}

// Export plans to CSV
exports.exportPlans = async (req, res) => {
    try {
        const { category = 'esim_realtime' } = req.query;

        // Get all plans for the specified category
        const whereClause = {
            category: category === 'topup' ? 'esim_addon' : category // Map 'topup' to 'esim_addon'
        };

        const plans = await EsimPlan.findAll({
            where: whereClause,
            include: [
                {
                    model: Country,
                    as: 'countries',
                    through: { attributes: [] }
                },
                {
                    model: Provider,
                    as: 'provider',
                    attributes: ['id', 'name', 'type', 'country']
                }
            ],
            order: [['createdAt', 'DESC']]
        });

        // Get all countries for mapping Mobimatter country codes
        const allCountries = await Country.findAll({
            attributes: ['id', 'name', 'iso3']
        });

        // Create a map for faster country lookups
        const countryMap = new Map();
        allCountries.forEach(country => {
            countryMap.set(country.name.toLowerCase(), country);
            countryMap.set(country.iso3.toLowerCase(), country);
            countryMap.set(country.id.toLowerCase(), country);
        });

        // Convert plans to CSV format
        const csvRows = [];

        // Add CSV header
        csvRows.push([
            'Product ID',
            'Name',
            'Network',
            'Validity (Days)',
            'Data',
            'Stock',
            'Provider',
            'Buying Price ($)',
            'Selling Price ($)',
            'Status',
            'Region',
            'Countries'
        ].join(','));

        // Add plan data
        for (const plan of plans) {
            const stockCount = (plan.provider?.type === 'API' ||
                               plan.provider?.name === 'mobimatter' ||
                               plan.provider?.name === 'BillionConnect' ||
                               plan.provider?.name === 'billionconnect') ? 'Unlimited' : await sequelize.models.EsimStock.count({
                where: {
                    esimPlanId: plan.id,
                    status: 'available'
                }
            });

            const dataValue = plan.planType === 'Unlimited' ? 'Unlimited' :
                            plan.planType === 'Custom' ? plan.customPlanData :
                            `${plan.planData} ${plan.planDataUnit}`;

            // Handle countries based on provider type
            let countries;
            if (plan.provider?.name?.toLowerCase() === 'mobimatter') {
                // For Mobimatter plans, get countries from providerMetadata
                const supportedCountries = plan.providerMetadata?.originalData?.supportedCountries || [];
                countries = supportedCountries
                    .map(countryCode => {
                        const country = countryMap.get(countryCode.toLowerCase());
                        return country ? country.name : countryCode;
                    })
                    .filter(Boolean)
                    .join('; ');
            } else {
                // For regular plans, use the associated countries
                countries = plan.countries.map(c => c.name).join('; ');
            }

            const regions = Array.isArray(plan.region) ? plan.region.join('; ') : plan.region;

            // Convert plan to JSON to ensure we can access all properties
            const planData = plan.toJSON ? plan.toJSON() : plan;

            csvRows.push([
                planData.productId || '',
                `"${(planData.name || '').replace(/"/g, '""')}"`,
                `"${(planData.networkName || '').replace(/"/g, '""')}"`,
                planData.validityDays || '',
                `"${dataValue || ''}"`,
                stockCount,
                `"${planData.provider?.name || 'N/A'}"`,
                planData.buyingPrice || '',
                planData.sellingPrice || '',
                planData.status || '',
                `"${regions || 'Global'}"`,
                `"${countries || ''}"`
            ].join(','));
        }

        const csvContent = csvRows.join('\n');

        // Set response headers for CSV download
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename=esim-plans-${category}-${new Date().toISOString().split('T')[0]}.csv`);

        // Send CSV content
        res.send(csvContent);
    } catch (error) {
        console.error('Error exporting plans:', error);
        res.status(500).json({
            message: 'Failed to export plans',
            error: error.message
        });
    }
};

// Export all controller functions
module.exports = {
    getAllPlans: exports.getAllPlans,
    getPlanById: exports.getPlanById,
    searchPlans: exports.searchPlans,
    getEsimPlans: exports.getEsimPlans,
    getEsimPlan: exports.getEsimPlan,
    createEsimPlan: exports.createEsimPlan,
    createBulkEsimPlans: exports.createBulkEsimPlans,
    updateEsimPlan: exports.updateEsimPlan,
    deleteEsimPlan: exports.deleteEsimPlan,
    updateEsimPlanStatus: exports.updateEsimPlanStatus,
    updateProviderPlansVisibility: exports.updateProviderPlansVisibility,
    updateEsimPlanPrice: exports.updateEsimPlanPrice,
    resetEsimPlanPrice: exports.resetEsimPlanPrice,
    getPartnerEsimPlans: exports.getPartnerEsimPlans,
    getPartnerEsimPlan: exports.getPartnerEsimPlan,
    toggleStartDate: exports.toggleStartDate,
    updateStockThreshold: exports.updateStockThreshold,
    syncExternalPlans: exports.syncExternalPlans,
    syncBillionConnectPlans: exports.syncBillionConnectPlans,
    syncMobimatterPlans: exports.syncMobimatterPlans,
    exportPlans: exports.exportPlans
};
