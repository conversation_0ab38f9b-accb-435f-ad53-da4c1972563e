const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const User = require('./User');
const EsimPlan = require('./EsimPlan');

const Cart = sequelize.define('Cart', {
    id: {
        type: DataTypes.CHAR(36),
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
    },
    userId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: User,
            key: 'id'
        }
    },
    esimPlanId: {
        type: DataTypes.CHAR(36),
        allowNull: false,
        references: {
            model: EsimPlan,
            key: 'id'
        }
    },
    quantity: {
        type: DataTypes.INTEGER,
        defaultValue: 1,
        validate: {
            min: 1
        }
    },
    isTopUp: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false
    },
    parentOrderId: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: 'External order ID of the parent order for top-up orders'
    }
}, {
    tableName: 'cart',
    timestamps: true
});

// Associations
Cart.belongsTo(User, { foreignKey: 'userId' });
Cart.belongsTo(EsimPlan, { foreignKey: 'esimPlanId' });

module.exports = Cart;