# BillionConnect Recharge Order API

This document describes the API endpoint for creating recharge orders for existing BillionConnect eSIM cards.

## Overview

The recharge order functionality allows you to add data plans to existing eSIM cards using their ICCID numbers. This is useful for customers who want to add more data to their existing eSIMs or extend their validity.

## API Endpoint

### Create Recharge Order

**POST** `/api/esim-plans/recharge-order`

Creates a recharge order for existing BillionConnect eSIM cards.

#### Authentication
- Requires authentication token
- Admin role required

#### Request Headers
```
Content-Type: application/json
Authorization: Bearer <your-jwt-token>
```

#### Request Body

```json
{
  "channelOrderId": "string (required)",
  "totalAmount": "string (optional)",
  "discountAmount": "string (optional)",
  "estimatedUseTime": "string (optional, YYYY-MM-DD format)",
  "orderCreateTime": "string (optional, YYYY-MM-DD HH:mm:ss format)",
  "comment": "string (optional)",
  "invoiceType": "string (optional, 0: individual; 1: company)",
  "invoiceHead": "string (optional)",
  "invoiceContent": "string (optional)",
  "invoiceComment": "string (optional)",
  "userId": "string (optional)",
  "subOrderList": [
    {
      "channelSubOrderId": "string (required)",
      "iccid": ["string (required, array of ICCID numbers, max 500)"],
      "skuId": "string (required, BillionConnect plan SKU ID)",
      "copies": "string (required, number of plans)",
      "price": "string (optional, selling price)",
      "discountAmount": "string (optional)"
    }
  ]
}
```

#### Request Example

```json
{
  "channelOrderId": "ORDER_20250701_001",
  "totalAmount": "50.00",
  "orderCreateTime": "2025-07-01 12:00:00",
  "comment": "Recharge order for customer data top-up",
  "subOrderList": [
    {
      "channelSubOrderId": "SUB_ORDER_001",
      "iccid": [
        "89860012017300000001",
        "89860012017300000002"
      ],
      "skuId": "1745221471583108",
      "copies": "2",
      "price": "25.00"
    }
  ]
}
```

#### Response

**Success Response (200 OK)**
```json
{
  "success": true,
  "message": "Recharge order created successfully",
  "data": {
    "orderId": "13131313131",
    "channelOrderId": "ORDER_20250701_001",
    "subOrderList": [
      {
        "subOrderId": "13131313132",
        "channelSubOrderId": "SUB_ORDER_001"
      }
    ],
    "providerResponse": {
      "tradeCode": "1000",
      "tradeMsg": "Success",
      "tradeData": {
        "orderId": "13131313131",
        "channelOrderId": "ORDER_20250701_001",
        "subOrderList": [
          {
            "subOrderId": "13131313132",
            "channelSubOrderId": "SUB_ORDER_001"
          }
        ]
      }
    }
  }
}
```

**Error Response (400 Bad Request)**
```json
{
  "error": "channelOrderId is required"
}
```

**Error Response (500 Internal Server Error)**
```json
{
  "error": "Failed to create recharge order",
  "details": "Detailed error message"
}
```

## Validation Rules

### Required Fields
- `channelOrderId`: Unique identifier for your order
- `subOrderList`: Array of sub-orders (must not be empty)
- `subOrderList[].channelSubOrderId`: Unique identifier for each sub-order
- `subOrderList[].iccid`: Array of ICCID numbers (1-500 items)
- `subOrderList[].skuId`: Valid BillionConnect plan SKU ID
- `subOrderList[].copies`: Number of plans to add

### Validation Constraints
- ICCID array cannot exceed 500 items per sub-order
- SKU ID must exist in the system and belong to BillionConnect provider
- All ICCID numbers should be valid format (typically 19-20 digits)

## Usage Examples

### Single eSIM Recharge
```bash
curl -X POST "https://your-api-domain.com/api/esim-plans/recharge-order" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "channelOrderId": "SINGLE_RECHARGE_001",
    "subOrderList": [
      {
        "channelSubOrderId": "SUB_001",
        "iccid": ["89860012017300000001"],
        "skuId": "1745221471583108",
        "copies": "1"
      }
    ]
  }'
```

### Multiple eSIM Recharge
```bash
curl -X POST "https://your-api-domain.com/api/esim-plans/recharge-order" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "channelOrderId": "BULK_RECHARGE_001",
    "totalAmount": "100.00",
    "subOrderList": [
      {
        "channelSubOrderId": "SUB_001",
        "iccid": [
          "89860012017300000001",
          "89860012017300000002",
          "89860012017300000003"
        ],
        "skuId": "1745221471583108",
        "copies": "3",
        "price": "75.00"
      },
      {
        "channelSubOrderId": "SUB_002",
        "iccid": ["89860012017300000004"],
        "skuId": "1745221471586109",
        "copies": "1",
        "price": "25.00"
      }
    ]
  }'
```

## Error Handling

The API will return appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid request data or validation errors
- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: Insufficient permissions (admin role required)
- **500 Internal Server Error**: Server-side errors or BillionConnect API errors

## Notes

1. This endpoint is specifically for BillionConnect eSIMs only
2. The ICCID numbers must belong to existing, active eSIM cards
3. The SKU ID must be a valid BillionConnect plan available in your system
4. Order IDs should be unique to avoid conflicts
5. The API will validate that the specified plan exists and belongs to BillionConnect before making the request

## Important: SKU ID Requirements for Recharge Orders

⚠️ **Current Status**: During testing, we encountered error code `1095` with message `请正确填写流量商品ID` (Please fill in the correct traffic product ID), even when using valid SKU IDs from the commodity listing API.

This suggests that:
1. **Recharge orders may require different SKU IDs** than those returned by the F002 (commodity listing) API
2. **Test environment limitations** - the test environment may not support recharge orders
3. **Additional prerequisites** may be required for recharge functionality

### Recommended Actions:
1. **Contact BillionConnect support** to clarify:
   - Which SKU IDs are valid for recharge orders
   - Whether recharge orders are supported in the test environment
   - Any additional setup required for recharge functionality

2. **Production Testing**: Test the functionality in the production environment with real ICCID numbers and confirmed recharge-compatible SKU IDs

3. **Alternative Approach**: Consider using the regular order creation API (F001) for new eSIM purchases instead of recharge orders

## Testing

A test script is available at `server/test_recharge_order.js` to test the functionality:

```bash
cd server
node test_recharge_order.js
```

This will test both successful order creation and validation error scenarios.

### Current Test Results:
✅ **Validation Tests**: All validation tests pass correctly
- Missing required fields are properly detected
- ICCID array size limits are enforced
- Invalid data structures are rejected

⚠️ **API Integration**: Currently returns error `1095` - "请正确填写流量商品ID"
- This indicates the SKU IDs from the commodity listing may not be valid for recharge orders
- Further investigation with BillionConnect support is needed to resolve this
